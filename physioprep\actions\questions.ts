"use server";

import axios from "axios";
import { revalidatePath } from "next/cache";
import {
  getApiUrl,
  checkAuth,
  handleServerActionError,
  handleRedirectError,
} from "@/services/api";
import type { Question, Subject, Topic, ApiResponse } from "@/types/types";

// Define the state types for form actions
export type QuestionFormState = {
  error: string | null;
  success: boolean;
  message?: string;
  data?: Question;
};

export type QuestionCreateData = {
  text: string;
  options: Array<{ text: string; isCorrect: boolean }>;
  explanationJson: string;
  explanationHtml: string;
  topic: string;
  difficulty: "easy" | "medium" | "hard";
  tier: "free" | "premium";
  isActive?: boolean;
};

export type QuestionUpdateData = Partial<QuestionCreateData>;

// Define admin stats type
export type QuestionAdminStats = {
  totalQuestions: number;
  activeQuestions: number;
  inactiveQuestions: number;
  freeQuestions: number;
  premiumQuestions: number;
  easyQuestions: number;
  mediumQuestions: number;
  hardQuestions: number;
  avgUsageCount: number;
  avgCorrectAnswerCount: number;
};

// Define questions response type with admin stats and pagination
export type QuestionsResponse = {
  questions: Question[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalQuestions: number;
    limit: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  stats?: QuestionAdminStats;
};

// Define filter parameters
export type QuestionFilters = {
  page?: number;
  limit?: number;
  search?: string;
  subject?: string;
  topic?: string;
  difficulty?: string;
  tier?: string;
  isActive?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
};

// Get all questions with filtering, sorting, and pagination
export async function getQuestions(
  filters: QuestionFilters = {}
): Promise<ApiResponse<QuestionsResponse>> {
  try {
    const authCheck = await checkAuth(true); // Require admin
    if (!authCheck.isAuthenticated) {
      return {
        success: false,
        errors: [{ msg: "Admin authentication required" }],
      };
    }

    const apiUrl = await getApiUrl();
    
    // Build query parameters
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        queryParams.append(key, value.toString());
      }
    });

    const response = await axios.get(
      `${apiUrl}/questions?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${authCheck.token}`,
        },
      }
    );

    return {
      success: true,
      data: {
        questions: response.data.data.questions || [],
        pagination: response.data.data.pagination || {
          currentPage: 1,
          totalPages: 1,
          totalQuestions: 0,
          limit: 10,
          hasNextPage: false,
          hasPrevPage: false,
        },
        stats: response.data.data.stats,
      },
    };
  } catch (error) {
    return await handleServerActionError(
      error as Error,
      "Failed to fetch questions"
    );
  }
}

// Get question by ID
export async function getQuestionById(id: string): Promise<ApiResponse<Question>> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        success: false,
        errors: [{ msg: "Admin authentication required" }],
      };
    }

    const apiUrl = await getApiUrl();
    const response = await axios.get(`${apiUrl}/questions/${id}`, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
      },
    });

    return {
      success: true,
      data: response.data.data || response.data,
    };
  } catch (error) {
    return await handleServerActionError(
      error as Error,
      "Failed to fetch question"
    );
  }
}

// Get all subjects for dropdown
export async function getSubjectsForDropdown(): Promise<ApiResponse<Subject[]>> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        success: false,
        errors: [{ msg: "Admin authentication required" }],
      };
    }

    const apiUrl = await getApiUrl();
    const response = await axios.get(`${apiUrl}/subjects`, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
      },
    });

    return {
      success: true,
      data: response.data.data || response.data,
    };
  } catch (error) {
    return await handleServerActionError(
      error as Error,
      "Failed to fetch subjects"
    );
  }
}

// Get topics by subject for dropdown
export async function getTopicsBySubject(subjectId: string): Promise<ApiResponse<Topic[]>> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        success: false,
        errors: [{ msg: "Admin authentication required" }],
      };
    }

    const apiUrl = await getApiUrl();
    const response = await axios.get(`${apiUrl}/topics?subject=${subjectId}`, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
      },
    });

    return {
      success: true,
      data: response.data.data?.topics || response.data.data || [],
    };
  } catch (error) {
    return await handleServerActionError(
      error as Error,
      "Failed to fetch topics"
    );
  }
}

// Create new question
export async function createQuestion(
  prevState: QuestionFormState,
  formData: FormData
): Promise<QuestionFormState> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        error: "Admin authentication required",
        success: false,
      };
    }

    // Extract form data
    const text = formData.get("text") as string;
    const explanationJson = formData.get("explanationJson") as string;
    const explanationHtml = formData.get("explanationHtml") as string;
    const topic = formData.get("topic") as string;
    const difficulty = formData.get("difficulty") as "easy" | "medium" | "hard";
    const tier = formData.get("tier") as "free" | "premium";
    const isActive = formData.get("isActive") === "on";

    // Parse options from form data
    const options: Array<{ text: string; isCorrect: boolean }> = [];
    let optionIndex = 0;
    while (formData.get(`option_${optionIndex}_text`)) {
      options.push({
        text: formData.get(`option_${optionIndex}_text`) as string,
        isCorrect: formData.get(`option_${optionIndex}_isCorrect`) === "on",
      });
      optionIndex++;
    }

    // Validate required fields
    if (!text || !topic || !difficulty || !tier || options.length < 2) {
      return {
        error: "Question text, topic, difficulty, tier, and at least 2 options are required",
        success: false,
      };
    }

    // Validate that at least one option is correct
    if (!options.some(option => option.isCorrect)) {
      return {
        error: "At least one option must be marked as correct",
        success: false,
      };
    }

    const questionData: QuestionCreateData = {
      text: text.trim(),
      options,
      explanationJson,
      explanationHtml,
      topic: topic.trim(),
      difficulty,
      tier,
      isActive,
    };

    const apiUrl = await getApiUrl();
    const response = await axios.post(`${apiUrl}/questions`, questionData, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
        "Content-Type": "application/json",
      },
    });

    if (response.data.success) {
      revalidatePath("/dashboard/questions");
      return {
        error: null,
        success: true,
        message: "Question created successfully",
        data: response.data.data,
      };
    }

    return {
      error: response.data.errors?.[0]?.msg || "Failed to create question",
      success: false,
    };
  } catch (error) {
    await handleRedirectError(error as Error);

    const errorResult = await handleServerActionError(
      error as Error,
      "Failed to create question"
    );
    return {
      error: errorResult.errors[0]?.msg || "Failed to create question",
      success: false,
    };
  }
}

// Update question
export async function updateQuestion(
  id: string,
  prevState: QuestionFormState,
  formData: FormData
): Promise<QuestionFormState> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        error: "Admin authentication required",
        success: false,
      };
    }

    // Extract form data
    const text = formData.get("text") as string;
    const explanationJson = formData.get("explanationJson") as string;
    const explanationHtml = formData.get("explanationHtml") as string;
    const topic = formData.get("topic") as string;
    const difficulty = formData.get("difficulty") as "easy" | "medium" | "hard";
    const tier = formData.get("tier") as "free" | "premium";
    const isActive = formData.get("isActive") === "on";

    // Parse options from form data
    const options: Array<{ text: string; isCorrect: boolean }> = [];
    let optionIndex = 0;
    while (formData.get(`option_${optionIndex}_text`)) {
      options.push({
        text: formData.get(`option_${optionIndex}_text`) as string,
        isCorrect: formData.get(`option_${optionIndex}_isCorrect`) === "on",
      });
      optionIndex++;
    }

    // Validate required fields
    if (!text || !topic || !difficulty || !tier || options.length < 2) {
      return {
        error: "Question text, topic, difficulty, tier, and at least 2 options are required",
        success: false,
      };
    }

    // Validate that at least one option is correct
    if (!options.some(option => option.isCorrect)) {
      return {
        error: "At least one option must be marked as correct",
        success: false,
      };
    }

    const questionData: QuestionUpdateData = {
      text: text.trim(),
      options,
      explanationJson,
      explanationHtml,
      topic: topic.trim(),
      difficulty,
      tier,
      isActive,
    };

    const apiUrl = await getApiUrl();
    const response = await axios.put(`${apiUrl}/questions/${id}`, questionData, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
        "Content-Type": "application/json",
      },
    });

    if (response.data.success) {
      revalidatePath("/dashboard/questions");
      revalidatePath(`/dashboard/questions/${id}`);
      return {
        error: null,
        success: true,
        message: "Question updated successfully",
        data: response.data.data,
      };
    }

    return {
      error: response.data.errors?.[0]?.msg || "Failed to update question",
      success: false,
    };
  } catch (error) {
    await handleRedirectError(error as Error);

    const errorResult = await handleServerActionError(
      error as Error,
      "Failed to update question"
    );
    return {
      error: errorResult.errors[0]?.msg || "Failed to update question",
      success: false,
    };
  }
}

// Delete question
export async function deleteQuestion(id: string): Promise<ApiResponse<{ message: string }>> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        success: false,
        errors: [{ msg: "Admin authentication required" }],
      };
    }

    const apiUrl = await getApiUrl();
    const response = await axios.delete(`${apiUrl}/questions/${id}`, {
      headers: {
        Authorization: `Bearer ${authCheck.token}`,
      },
    });

    if (response.data.success) {
      revalidatePath("/dashboard/questions");
      return {
        success: true,
        data: { message: "Question deleted successfully" },
      };
    }

    return {
      success: false,
      errors: [{ msg: response.data.errors?.[0]?.msg || "Failed to delete question" }],
    };
  } catch (error) {
    return await handleServerActionError(
      error as Error,
      "Failed to delete question"
    );
  }
}

// Bulk delete questions
export async function bulkDeleteQuestions(questionIds: string[]): Promise<ApiResponse<{ message: string }>> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        success: false,
        errors: [{ msg: "Admin authentication required" }],
      };
    }

    const apiUrl = await getApiUrl();
    const response = await axios.post(`${apiUrl}/questions/bulk-delete`,
      { questionIds },
      {
        headers: {
          Authorization: `Bearer ${authCheck.token}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (response.data.success) {
      revalidatePath("/dashboard/questions");
      return {
        success: true,
        data: { message: `${questionIds.length} questions deleted successfully` },
      };
    }

    return {
      success: false,
      errors: [{ msg: response.data.errors?.[0]?.msg || "Failed to delete questions" }],
    };
  } catch (error) {
    return await handleServerActionError(
      error as Error,
      "Failed to delete questions"
    );
  }
}

// Bulk update questions
export async function bulkUpdateQuestions(
  questionIds: string[],
  updates: Partial<QuestionUpdateData>
): Promise<ApiResponse<{ message: string }>> {
  try {
    const authCheck = await checkAuth(true);
    if (!authCheck.isAuthenticated) {
      return {
        success: false,
        errors: [{ msg: "Admin authentication required" }],
      };
    }

    const apiUrl = await getApiUrl();
    const response = await axios.put(`${apiUrl}/questions/bulk-update`,
      { questionIds, updates },
      {
        headers: {
          Authorization: `Bearer ${authCheck.token}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (response.data.success) {
      revalidatePath("/dashboard/questions");
      return {
        success: true,
        data: { message: `${questionIds.length} questions updated successfully` },
      };
    }

    return {
      success: false,
      errors: [{ msg: response.data.errors?.[0]?.msg || "Failed to update questions" }],
    };
  } catch (error) {
    return await handleServerActionError(
      error as Error,
      "Failed to update questions"
    );
  }
}
