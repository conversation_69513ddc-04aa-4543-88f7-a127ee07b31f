import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { 
  Table, 
  Plus, 
  Minus, 
  Trash2, 
  ChevronDown,
  RowsIcon,
  ColumnsIcon
} from "lucide-react";
import { EditorBubbleItem, useEditor } from "novel";

interface TableSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const tableActions = [
  {
    name: "Add Column Before",
    action: "addColumnBefore",
    icon: Plus,
    description: "Insert column before current",
  },
  {
    name: "Add Column After",
    action: "addColumnAfter",
    icon: Plus,
    description: "Insert column after current",
  },
  {
    name: "Delete Column",
    action: "deleteColumn",
    icon: Minus,
    description: "Delete current column",
  },
  {
    name: "Add Row Before",
    action: "addRowBefore",
    icon: RowsIcon,
    description: "Insert row before current",
  },
  {
    name: "Add Row After",
    action: "addRowAfter",
    icon: RowsIcon,
    description: "Insert row after current",
  },
  {
    name: "Delete Row",
    action: "deleteRow",
    icon: Minus,
    description: "Delete current row",
  },
  {
    name: "Delete Table",
    action: "deleteTable",
    icon: Trash2,
    description: "Delete entire table",
  },
  {
    name: "Toggle Header Row",
    action: "toggleHeaderRow",
    icon: ColumnsIcon,
    description: "Toggle header row",
  },
];

export const TableSelector = ({ open, onOpenChange }: TableSelectorProps) => {
  const { editor } = useEditor();

  if (!editor) return null;

  const isInTable = editor.isActive("table");
  const isInTableCell = editor.isActive("tableCell") || editor.isActive("tableHeader");

  // console.log("Table selector - isInTable:", isInTable, "isInTableCell:", isInTableCell);

  if (!isInTable && !isInTableCell) return null;

  const executeTableAction = (action: string) => {
    console.log(`Executing table action: ${action}`);
    console.log("Editor is active in table:", editor.isActive("table"));
    console.log("Available commands:", Object.keys(editor.commands));

    // Ensure we're focused and in a table
    if (!editor.isActive("table")) {
      console.warn("Not in a table, cannot execute table action");
      return;
    }

    let result = false;

    // Try to ensure we're in a table cell
    const { state } = editor;
    const { selection } = state;
    console.log("Current selection:", selection);

    switch (action) {
      case "addColumnBefore":
        result = editor.commands.addColumnBefore();
        if (!result) {
          // Try alternative approach
          result = editor.chain().focus().addColumnBefore().run();
        }
        console.log("addColumnBefore result:", result);
        break;
      case "addColumnAfter":
        result = editor.commands.addColumnAfter();
        if (!result) {
          result = editor.chain().focus().addColumnAfter().run();
        }
        console.log("addColumnAfter result:", result);
        break;
      case "deleteColumn":
        result = editor.commands.deleteColumn();
        if (!result) {
          result = editor.chain().focus().deleteColumn().run();
        }
        console.log("deleteColumn result:", result);
        break;
      case "addRowBefore":
        result = editor.commands.addRowBefore();
        if (!result) {
          result = editor.chain().focus().addRowBefore().run();
        }
        console.log("addRowBefore result:", result);
        break;
      case "addRowAfter":
        result = editor.commands.addRowAfter();
        if (!result) {
          result = editor.chain().focus().addRowAfter().run();
        }
        console.log("addRowAfter result:", result);
        break;
      case "deleteRow":
        result = editor.commands.deleteRow();
        if (!result) {
          result = editor.chain().focus().deleteRow().run();
        }
        console.log("deleteRow result:", result);
        break;
      case "deleteTable":
        result = editor.commands.deleteTable();
        if (!result) {
          result = editor.chain().focus().deleteTable().run();
        }
        console.log("deleteTable result:", result);
        break;
      case "toggleHeaderRow":
        result = editor.commands.toggleHeaderRow();
        if (!result) {
          result = editor.chain().focus().toggleHeaderRow().run();
        }
        console.log("toggleHeaderRow result:", result);
        break;
      default:
        console.warn(`Unknown table action: ${action}`);
        break;
    }

    if (!result) {
      console.error(`Table action ${action} failed`);
    }

    onOpenChange(false);
  };

  return (
    <Popover modal={true} open={open} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>
        <Button variant="ghost" className="gap-2 rounded-none border-none">
          <Table className="h-4 w-4" />
          <span className="whitespace-nowrap text-sm">Table</span>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent sideOffset={5} align="start" className="w-56 p-1">
        <div className="flex flex-col gap-1">
          {/* Debug info */}
          <div className="px-2 py-1 text-xs text-muted-foreground border-b">
            Table: {isInTable ? "✓" : "✗"} | Cell: {isInTableCell ? "✓" : "✗"}
          </div>

          {tableActions.map((action) => (
            <EditorBubbleItem
              key={action.action}
              onSelect={() => executeTableAction(action.action)}
              className="flex cursor-pointer items-center justify-between rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
            >
              <div className="flex items-center gap-2">
                <action.icon className="h-4 w-4" />
                <span>{action.name}</span>
              </div>
            </EditorBubbleItem>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};
