const express = require("express");
const router = express.Router();
const { body, param } = require("express-validator");

// Import controllers
const {
  getUsers,
  getUser,
  updateUser,
  deleteUser,
  getUserStats,
  getUserAnalytics,
  getUserProgress,
  getUserHistory,
  getUserLeaderboard,
  updateUserPreferences,
  updateUserSubscription,
  resetUserPassword,
  deactivateUser,
  reactivateUser,
  getProfile,
  updateProfile,
} = require("../controllers/userController");

// Import middleware
const { auth, admin } = require("../middleware/auth");
const { handleValidationErrors } = require("../validators/authValidators");

// Validation for user update
const validateUserUpdate = [
  body("firstName")
    .optional()
    .trim()
    .notEmpty()
    .withMessage("First name cannot be empty"),

  body("lastName")
    .optional()
    .trim()
    .notEmpty()
    .withMessage("Last name cannot be empty"),

  body("email")
    .optional()
    .trim()
    .isEmail()
    .withMessage("Please enter a valid email"),

  body("role").optional().isIn(["user", "admin"]).withMessage("Invalid role"),

  body("tier").optional().isIn(["free", "premium"]).withMessage("Invalid tier"),

  handleValidationErrors,
];

// Validation for preferences update
const validatePreferences = [
  body("emailNotifications")
    .optional()
    .isBoolean()
    .withMessage("Email notifications must be a boolean"),

  body("pushNotifications")
    .optional()
    .isBoolean()
    .withMessage("Push notifications must be a boolean"),

  body("theme")
    .optional()
    .isIn(["light", "dark", "system"])
    .withMessage("Invalid theme"),

  handleValidationErrors,
];

// Validation for subscription update
const validateSubscription = [
  body("status")
    .isIn(["active", "inactive", "cancelled"])
    .withMessage("Invalid subscription status"),

  body("plan")
    .isIn(["monthly", "yearly"])
    .withMessage("Invalid subscription plan"),

  body("startDate").optional().isISO8601().withMessage("Invalid start date"),

  body("endDate").optional().isISO8601().withMessage("Invalid end date"),

  handleValidationErrors,
];

// Public routes
router.get("/leaderboard", getUserLeaderboard);

// User routes (requires authentication)
router.get("/me", auth, getUser);
router.get("/me/stats", auth, getUserStats);
router.get("/me/progress", auth, getUserProgress);
router.get("/me/history", auth, getUserHistory);
router.put("/me", auth, validateUserUpdate, updateUser);
router.put("/me/preferences", auth, validatePreferences, updateUserPreferences);
router.put(
  "/me/subscription",
  auth,
  validateSubscription,
  updateUserSubscription
);
router.post("/me/reset-password", auth, resetUserPassword);

// Profile routes (requires authentication)
router.get("/profile", auth, getProfile);
router.put("/profile", auth, updateProfile);

router.post("/me/deactivate", auth, deactivateUser);
router.post("/me/reactivate", auth, reactivateUser);

// Admin routes
router.get("/", auth, admin, getUsers);
router.get(
  "/:id",
  auth,
  admin,
  param("id").isMongoId(),
  handleValidationErrors,
  getUser
);
router.get(
  "/:id/stats",
  auth,
  admin,
  param("id").isMongoId(),
  handleValidationErrors,
  getUserStats
);
router.get(
  "/:id/analytics",
  auth,
  admin,
  param("id").isMongoId(),
  handleValidationErrors,
  getUserAnalytics
);
router.get(
  "/:id/progress",
  auth,
  admin,
  param("id").isMongoId(),
  handleValidationErrors,
  getUserProgress
);
router.get(
  "/:id/history",
  auth,
  admin,
  param("id").isMongoId(),
  handleValidationErrors,
  getUserHistory
);
router.put(
  "/:id",
  auth,
  admin,
  param("id").isMongoId(),
  validateUserUpdate,
  handleValidationErrors,
  updateUser
);
router.delete(
  "/:id",
  auth,
  admin,
  param("id").isMongoId(),
  handleValidationErrors,
  deleteUser
);

module.exports = router;
