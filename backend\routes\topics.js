const express = require("express");
const router = express.Router();
const topicController = require("../controllers/topicController");
const { body, param } = require("express-validator");
const { optionalAuth, auth, admin } = require("../middleware/auth");
const { handleValidationErrors } = require("../validators/authValidators");

// Validation for topic creation/update
const validateTopic = [
  body('topicName')
    .trim()
    .notEmpty()
    .withMessage('Topic name is required')
    .isLength({ max: 100 })
    .withMessage('Topic name cannot exceed 100 characters'),

  body('descriptionJson')
    .optional()
    .custom((value) => {
      if (value && typeof value === 'string') {
        try {
          JSON.parse(value);
        } catch (e) {
          throw new Error('Description JSON must be valid JSON');
        }
      }
      return true;
    }),

  body('descriptionHtml')
    .optional()
    .trim(),

  body('subject')
    .notEmpty()
    .withMessage('Subject is required')
    .isMongoId()
    .withMessage('Subject must be a valid ID'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),

  body('isPremium')
    .optional()
    .isBoolean()
    .withMessage('isPremium must be a boolean'),

  handleValidationErrors
];

const validateTopicUpdate = [
  body('topicName')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('Topic name cannot be empty')
    .isLength({ max: 100 })
    .withMessage('Topic name cannot exceed 100 characters'),

  body('descriptionJson')
    .optional()
    .custom((value) => {
      if (value && typeof value === 'string') {
        try {
          JSON.parse(value);
        } catch (e) {
          throw new Error('Description JSON must be valid JSON');
        }
      }
      return true;
    }),

  body('descriptionHtml')
    .optional()
    .trim(),

  body('subject')
    .optional()
    .isMongoId()
    .withMessage('Subject must be a valid ID'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),

  body('isPremium')
    .optional()
    .isBoolean()
    .withMessage('isPremium must be a boolean'),

  handleValidationErrors
];

// Public routes (with optional auth to detect admin users)
router.get("/", auth, topicController.getTopics);
router.get("/:id", param('id').isMongoId(), handleValidationErrors, auth, topicController.getTopic);

// Admin routes
router.post("/", auth, admin, validateTopic, topicController.createTopic);
router.put("/:id", auth, admin, param('id').isMongoId(), validateTopicUpdate, handleValidationErrors, topicController.updateTopic);
router.delete("/:id", auth, admin, param('id').isMongoId(), handleValidationErrors, topicController.deleteTopic);

module.exports = router;
