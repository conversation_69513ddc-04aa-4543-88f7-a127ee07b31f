import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Alert, RefreshControl, ActivityIndicator, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { FadeInDown, FadeInRight, useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';
import { useAuth } from '../../hooks/useAuth';
import { useUser } from '../../store/auth';
import api from '../../services/api';

interface ProfileStats { totalQuizzesTaken: number; totalTestsTaken: number; correctAnswers: number; averageScore: number; currentStreak: number; longestStreak: number; totalQuestionsAnswered?: number; }

const StatsCard = ({ title, value, colors }: { title: string; value: string | number; colors: readonly [string, string] }) => (
  <View className="flex-1 min-w-[45%]">
    <LinearGradient colors={colors as any} className="rounded-2xl p-4">
      <Text className="text-2xl font-bold text-white">{String(value)}</Text>
      <Text className="text-white/80 text-sm">{title}</Text>
    </LinearGradient>
  </View>
);

export default function UserProfile() {
  const router = useRouter();
  const { logout, updateUser } = useAuth();
  const [user, setUser] = useUser();
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState<ProfileStats | null>(null);
  const [loadingProfile, setLoadingProfile] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [savingPrefs, setSavingPrefs] = useState(false);
  const scale = useSharedValue(1);

  const [prefs, setPrefs] = useState({
    notifications: user?.preferences?.notifications ?? true,
    dailyReminder: user?.preferences?.dailyReminder ?? false,
    theme: (user?.preferences?.theme as 'light' | 'dark' | 'auto') ?? 'auto',
  });

  const applyPrefsUpdate = async (partial: Partial<typeof prefs>) => {
    const prev = prefs; const next = { ...prefs, ...partial }; setPrefs(next);
    try { setSavingPrefs(true); await updateUser({ preferences: next } as any); }
    catch { setPrefs(prev); Alert.alert('Update failed', 'Could not update preferences. Please try again.'); }
    finally { setSavingPrefs(false); }
  };

  useEffect(() => { fetchUserProfile(); }, []);

  const fetchUserProfile = async () => {
    try {
      setError(null); setLoadingProfile(true);
      const res = await api.get('/users/profile');
      if (res.data?.success) {
        const fresh = res.data.data; setStats(fresh?.stats || null); setUser(fresh);
        setPrefs({ notifications: fresh?.preferences?.notifications ?? true, dailyReminder: fresh?.preferences?.dailyReminder ?? false, theme: (fresh?.preferences?.theme as 'light' | 'dark' | 'auto') ?? 'auto' });
      } else { setError('Failed to load profile'); }
    } catch (e: any) { console.error('Failed to fetch profile:', e); setError(e?.response?.data?.errors?.[0]?.msg || 'Failed to load profile'); }
    finally { setLoadingProfile(false); }
  };

  const onRefresh = async () => { setRefreshing(true); await fetchUserProfile(); setRefreshing(false); };

  const formatDate = (d: string | null) => (d ? new Date(d).toLocaleDateString() : 'Never');
  const getPremiumStatus = () => { if (!user?.isPremium) return { text: 'Free', color: '#6B7280' as const }; if (user.isPremiumActive) return { text: 'Premium Active', color: '#10B981' as const }; return { text: 'Premium Expired', color: '#EF4444' as const }; };

  const buttonAnimatedStyle = useAnimatedStyle(() => ({ transform: [{ scale: scale.value }] }));
  const handlePressIn = () => { scale.value = withSpring(0.95); };
  const handlePressOut = () => { scale.value = withSpring(1); };

  const premiumStatus = getPremiumStatus();

  const menuItems = [
    { title: 'Account Settings', description: 'Manage your account preferences', icon: 'settings-outline' as const, route: '/profile', colors: ['#6366F1', '#8B5CF6'] as const },
    { title: 'Change Password', description: 'Update your password', icon: 'lock-closed-outline' as const, route: '/profile/password', colors: ['#F59E0B', '#EF4444'] as const },
    { title: 'Statistics', description: 'View detailed performance stats', icon: 'bar-chart-outline' as const, route: '/profile/stats', colors: ['#10B981', '#059669'] as const },
    { title: 'Delete Account', description: 'Permanently delete your account', icon: 'trash-outline' as const, route: '/profile/delete', colors: ['#EF4444', '#DC2626'] as const },
  ];

  return (
    <SafeAreaView className="flex-1 bg-[#0F0F0F]">
      <ScrollView className="flex-1" refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}>
        <View className="p-6">
          <Animated.View entering={FadeInDown.delay(100).springify()} className="flex-row justify-between items-center mb-6">
            <Text className="text-3xl font-bold text-white">Profile</Text>
            <TouchableOpacity accessibilityRole="button" accessibilityLabel="Close profile" onPress={() => router.back()} className="w-10 h-10 rounded-full bg-gray-800 items-center justify-center">
              <Ionicons name="close" size={24} color="white" />
            </TouchableOpacity>
          </Animated.View>

          {!!error && (
            <View className="mb-4 rounded-xl bg-red-500/15 border border-red-500/30 p-3">
              <Text className="text-red-400">{error}</Text>
              <TouchableOpacity onPress={onRefresh} className="mt-2 self-start px-3 py-1 rounded-lg bg-red-500/20"><Text className="text-red-300 text-sm">Retry</Text></TouchableOpacity>
            </View>
          )}

          {loadingProfile && (
            <View className="mb-4 items-center"><ActivityIndicator color="#FF6B6B" /></View>
          )}

          {/* User Info */}
          <Animated.View entering={FadeInDown.delay(200).springify()} className="mb-6">
            <LinearGradient colors={['#1F2937', '#374151']} className="rounded-3xl p-6">
              <View className="flex-row items-center mb-4">
                <View className="w-20 h-20 rounded-full overflow-hidden mr-4 border border-white/10">
                  {user?.avatar ? (
                    <Image source={{ uri: user.avatar }} className="w-full h-full" resizeMode="cover" />
                  ) : (
                    <LinearGradient colors={['#6366F1', '#8B5CF6']} className="flex-1 justify-center items-center">
                      <Text className="text-3xl font-bold text-white">{user?.name?.[0]?.toUpperCase() || 'U'}</Text>
                    </LinearGradient>
                  )}
                </View>
                <View className="flex-1">
                  <Text className="text-2xl font-bold text-white mb-1">{user?.name || 'User'}</Text>
                  <Text className="text-gray-300 mb-2">{user?.email}</Text>
                  <View className="flex-row items-center">
                    <View className="px-3 py-1 rounded-full mr-2" style={{ backgroundColor: `${premiumStatus.color}20` }}>
                      <Text style={{ color: premiumStatus.color }} className="text-sm font-semibold">{premiumStatus.text}</Text>
                    </View>
                    {user?.role === 'admin' && (<View className="px-3 py-1 rounded-full bg-purple-500/20"><Text className="text-purple-400 text-sm font-semibold">Admin</Text></View>)}
                  </View>
                </View>
              </View>
              {user?.isPremium && user?.premiumExpiry && (
                <View className="border-t border-gray-600 pt-4"><Text className="text-gray-400 text-sm">Premium expires: {formatDate(user.premiumExpiry)}</Text></View>
              )}
            </LinearGradient>
          </Animated.View>

          {/* Stats */}
          <Animated.View entering={FadeInDown.delay(300).springify()} className="mb-6">
            <Text className="text-xl font-bold text-white mb-4">Statistics</Text>
            <View className="flex-row flex-wrap gap-3">
              <StatsCard title="Quizzes Taken" value={stats?.totalQuizzesTaken ?? user?.stats?.totalQuizzesTaken ?? 0} colors={['#10B981','#059669']} />
              <StatsCard title="Tests Taken" value={stats?.totalTestsTaken ?? user?.stats?.totalTestsTaken ?? 0} colors={['#22D3EE','#06B6D4']} />
              <StatsCard title="Average Score" value={`${Math.round((stats?.averageScore ?? user?.stats?.averageScore ?? 0) as number)}%`} colors={['#6366F1','#8B5CF6']} />
              <StatsCard title="Current Streak" value={stats?.currentStreak ?? user?.stats?.currentStreak ?? 0} colors={['#F59E0B','#D97706']} />
              <StatsCard title="Correct Answers" value={stats?.correctAnswers ?? user?.stats?.correctAnswers ?? 0} colors={['#EF4444','#DC2626']} />
              <StatsCard title="Total Answered" value={stats?.totalQuestionsAnswered ?? user?.stats?.totalQuestionsAnswered ?? 0} colors={['#A78BFA','#7C3AED']} />
            </View>
          </Animated.View>

          {/* Preferences */}
          <Animated.View entering={FadeInDown.delay(400).springify()} className="mb-6">
            <Text className="text-xl font-bold text-white mb-4">Preferences</Text>
            <LinearGradient colors={['#1F2937', '#374151']} className="rounded-2xl p-4">
              <View className="flex-row justify-between items-center mb-3">
                <Text className="text-white">Notifications</Text>
                <TouchableOpacity accessibilityRole="switch" accessibilityState={{ checked: !!prefs.notifications }} disabled={savingPrefs} onPress={() => applyPrefsUpdate({ notifications: !prefs.notifications })} className={`w-12 h-7 rounded-full ${prefs.notifications ? 'bg-green-500/60' : 'bg-gray-600'} justify-center`}>
                  <View className={`w-6 h-6 rounded-full bg-white ${prefs.notifications ? 'ml-6' : 'ml-1'}`} />
                </TouchableOpacity>
              </View>
              <View className="flex-row justify-between items-center mb-3">
                <Text className="text-white">Daily Reminder</Text>
                <TouchableOpacity accessibilityRole="switch" accessibilityState={{ checked: !!prefs.dailyReminder }} disabled={savingPrefs} onPress={() => applyPrefsUpdate({ dailyReminder: !prefs.dailyReminder })} className={`w-12 h-7 rounded-full ${prefs.dailyReminder ? 'bg-green-500/60' : 'bg-gray-600'} justify-center`}>
                  <View className={`w-6 h-6 rounded-full bg-white ${prefs.dailyReminder ? 'ml-6' : 'ml-1'}`} />
                </TouchableOpacity>
              </View>
              <View className="mb-2">
                <Text className="text-white mb-2">Theme</Text>
                <View className="flex-row bg-black/20 rounded-xl p-1">
                  {(['auto','light','dark'] as const).map((t) => (
                    <TouchableOpacity key={t} disabled={savingPrefs} onPress={() => applyPrefsUpdate({ theme: t })} className={`flex-1 py-2 rounded-lg ${prefs.theme === t ? 'bg-white/10' : ''}`} accessibilityRole="button" accessibilityState={{ selected: prefs.theme === t }} accessibilityLabel={`Set theme to ${t}`}>
                      <Text className={`text-center ${prefs.theme === t ? 'text-white' : 'text-gray-300'}`}>{t.toUpperCase()}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              {savingPrefs && (<Text className="text-xs text-gray-400 mt-1">Saving...</Text>)}
            </LinearGradient>
          </Animated.View>

          {/* Account */}
          <Animated.View entering={FadeInDown.delay(500).springify()} className="mb-6">
            <Text className="text-xl font-bold text-white mb-4">Account</Text>
            <View className="space-y-3">
              {menuItems.map((item, index) => (
                <Animated.View key={item.title} entering={FadeInRight.delay(600 + index * 100).springify()} style={buttonAnimatedStyle}>
                  <TouchableOpacity accessibilityRole="button" accessibilityLabel={item.title} onPress={() => router.push(item.route as any)} onPressIn={handlePressIn} onPressOut={handlePressOut} className="rounded-2xl overflow-hidden">
                    <LinearGradient colors={item.colors} className="p-4 flex-row items-center">
                      <Ionicons name={item.icon} size={24} color="white" />
                      <View className="flex-1 ml-4">
                        <Text className="text-lg font-bold text-white">{item.title}</Text>
                        <Text className="text-white/80 text-sm">{item.description}</Text>
                      </View>
                      <Ionicons name="chevron-forward" size={20} color="white" />
                    </LinearGradient>
                  </TouchableOpacity>
                </Animated.View>
              ))}
            </View>
          </Animated.View>

          {/* Logout */}
          <Animated.View entering={FadeInDown.delay(1000).springify()}>
            <TouchableOpacity accessibilityRole="button" accessibilityLabel="Logout" onPress={() => Alert.alert('Logout','Are you sure you want to logout?',[{ text: 'Cancel', style: 'cancel' },{ text: 'Logout', style: 'destructive', onPress: logout }])} onPressIn={handlePressIn} onPressOut={handlePressOut} style={buttonAnimatedStyle} className="bg-red-500/20 border border-red-500/30 p-4 rounded-2xl flex-row items-center justify-center">
              <Ionicons name="log-out-outline" size={24} color="#EF4444" />
              <Text className="text-red-500 font-bold ml-2 text-lg">Logout</Text>
            </TouchableOpacity>
          </Animated.View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

