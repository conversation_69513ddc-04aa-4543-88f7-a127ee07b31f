import { Suspense } from "react";
import Link from "next/link";
import { Plus, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { QuestionDataTable } from "@/components/admin/questions/QuestionDataTable";
import { QuestionStats } from "@/components/admin/questions/QuestionStats";
import { getQuestions, QuestionAdminStats } from "@/actions/questions";

function QuestionsTableSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="h-8 w-48 bg-gray-200 rounded animate-pulse" />
        <div className="h-8 w-24 bg-gray-200 rounded animate-pulse" />
      </div>
      <div className="space-y-2">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="h-16 bg-gray-100 rounded animate-pulse" />
        ))}
      </div>
    </div>
  );
}

export interface QuestionSidebarFilters {
  search?: string;
  subject?: string;
  topic?: string;
  difficulty?: string;
  tier?: string;
  isActive?: string;
}

// function RefreshButton({
//   onRefresh,
//   isLoading,
// }: {
//   onRefresh: () => void;
//   isLoading: boolean;
// }) {
//   return (
//     <Button
//       variant="outline"
//       size="sm"
//       onClick={onRefresh}
//       disabled={isLoading}
//     >
//       <RefreshCw
//         className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
//       />
//       Refresh
//     </Button>
//   );
// }

export default async function QuestionsPage() {

  try {
    const [questionsResult] = await Promise.all([
      getQuestions(),
    ]);
    

    const questions = questionsResult.success
      ? questionsResult.data.questions
      : [];
    const pagination = questionsResult.success
      ? questionsResult.data.pagination
      : {
          currentPage: 1,
          totalPages: 1,
          totalQuestions: 0,
          limit: 10,
          hasNextPage: false,
          hasPrevPage: false,
        };
    const stats = questionsResult.success ? questionsResult.data.stats as QuestionAdminStats : null;


    if (!questionsResult.success) {
      return (
        <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <p className="text-muted-foreground">
                {questionsResult.errors[0]?.msg || "Failed to load questions"}
              </p>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center space-x-2 mb-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
        </div>

        <div className="flex items-center justify-between space-y-2">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Questions</h2>
            <p className="text-muted-foreground">
              Manage questions across different subjects and topics. Create,
              edit, and organize your question bank.
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {/* <RefreshButton onRefresh={fetchData} isLoading={isLoading} /> */}
            <Button asChild>
              <Link href="/dashboard/questions/create">
                <Plus className="mr-2 h-4 w-4" />
                Add Question
              </Link>
            </Button>
          </div>
        </div>

        {/* Stats Section */}
        {stats && (
          <QuestionStats stats={stats} />
        )}

        {/* Questions Table */}
        <Card>
          <CardHeader>
            <CardTitle>All Questions</CardTitle>
            <CardDescription>
              A list of all questions in the system. You can view, edit, filter,
              search, or delete questions from here.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<QuestionsTableSkeleton />}>
              <QuestionDataTable
                questions={questions}
                pagination={pagination}
              />
            </Suspense>
          </CardContent>
        </Card>
      </div>
    );
  } catch (error) {
    console.error("Error loading questions page:", error);
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-2">Error</h2>
            <p className="text-muted-foreground">
              Failed to load questions. Please try again later.
            </p>
            <Button onClick={()=> window.location.reload()} className="mt-4">
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }
}
