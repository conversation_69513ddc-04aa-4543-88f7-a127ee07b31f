@import "tailwindcss";
@import "tw-animate-css";
@plugin "@tailwindcss/typography";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.21 0.006 285.885);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.705 0.015 286.067);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.21 0.006 285.885);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.705 0.015 286.067);
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.92 0.004 286.32);
  --primary-foreground: oklch(0.21 0.006 285.885);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.552 0.016 285.938);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.552 0.016 285.938);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Rich text content styles */
.rich-text-content {
  @apply text-foreground;
}

/* YouTube video styles for better presentation */
.ProseMirror iframe[src*="youtube"] {
  @apply w-full max-w-full rounded-lg shadow-sm border border-muted;
  aspect-ratio: 16 / 9;
  min-height: 315px;
}

.ProseMirror .youtube-wrapper {
  @apply my-4 mx-auto max-w-4xl;
}

/* Ensure YouTube videos are responsive */
.ProseMirror [data-youtube-video] {
  @apply relative w-full overflow-hidden rounded-lg bg-muted/50;
  aspect-ratio: 16 / 9;
}

.ProseMirror [data-youtube-video] iframe {
  @apply absolute inset-0 w-full h-full border-0;
}

/* Text alignment styles */
.ProseMirror [style*="text-align: left"] {
  text-align: left !important;
}

.ProseMirror [style*="text-align: center"] {
  text-align: center !important;
}

.ProseMirror [style*="text-align: right"] {
  text-align: right !important;
}

.ProseMirror [style*="text-align: justify"] {
  text-align: justify !important;
}

/* Ensure alignment works with all text elements */
.ProseMirror p[style*="text-align"],
.ProseMirror h1[style*="text-align"],
.ProseMirror h2[style*="text-align"],
.ProseMirror h3[style*="text-align"],
.ProseMirror h4[style*="text-align"],
.ProseMirror h5[style*="text-align"],
.ProseMirror h6[style*="text-align"] {
  display: block;
  width: 100%;
}

/* Table styles */
.ProseMirror table {
  @apply border-collapse table-auto w-full border border-muted my-4;
  border-spacing: 0;
}

.ProseMirror table td,
.ProseMirror table th {
  @apply border border-muted px-4 py-2 relative;
  min-width: 1em;
  vertical-align: top;
}

.ProseMirror table th {
  @apply bg-muted/50 font-medium text-left;
}

.ProseMirror table .selectedCell:after {
  @apply bg-primary/20 absolute inset-0 pointer-events-none;
  content: "";
}

.ProseMirror table .column-resize-handle {
  @apply absolute right-0 top-0 bottom-0 w-1 bg-primary/50 pointer-events-auto cursor-col-resize;
}

/* Details/Collapsible styles */
.ProseMirror details {
  @apply my-4 rounded-lg border border-muted bg-muted/20 overflow-hidden;
}

.ProseMirror details summary {
  @apply cursor-pointer px-4 py-2 font-medium hover:bg-muted/50 transition-colors;
  list-style: none;
}

.ProseMirror details summary::-webkit-details-marker {
  display: none;
}

.ProseMirror details summary::before {
  @apply inline-block w-4 h-4 mr-2 transition-transform;
  content: "▶";
}

.ProseMirror details[open] summary::before {
  transform: rotate(90deg);
}

.ProseMirror details .details-content {
  @apply px-4 py-2 border-t border-muted;
}

/* Table resize cursor */
.ProseMirror .tableWrapper {
  overflow-x: auto;
}

.ProseMirror .resize-cursor {
  cursor: col-resize;
}

.rich-text-content h1 {
  @apply text-3xl font-bold mb-6 mt-8 text-foreground;
}

.rich-text-content h2 {
  @apply text-2xl font-semibold mb-4 mt-6 text-foreground;
}

.rich-text-content h3 {
  @apply text-xl font-semibold mb-3 mt-5 text-foreground;
}

.rich-text-content h4 {
  @apply text-lg font-semibold mb-2 mt-4 text-foreground;
}

.rich-text-content h5 {
  @apply text-base font-semibold mb-2 mt-3 text-foreground;
}

.rich-text-content h6 {
  @apply text-sm font-semibold mb-2 mt-2 text-foreground;
}

.rich-text-content p {
  @apply mb-4 leading-relaxed text-foreground;
}

.rich-text-content ul {
  @apply list-disc list-inside mb-4 space-y-1 ml-4;
}

.rich-text-content ol {
  @apply list-decimal list-inside mb-4 space-y-1 ml-4;
}

.rich-text-content li {
  @apply ml-4 text-foreground;
}

.rich-text-content blockquote {
  @apply border-l-4 border-primary pl-4 italic my-4 text-muted-foreground bg-muted/30 py-2 rounded-r;
}

.rich-text-content pre {
  @apply bg-muted p-4 rounded-md overflow-x-auto mb-4 text-sm;
}

.rich-text-content code {
  @apply bg-muted px-1 py-0.5 rounded text-sm font-mono;
}

.rich-text-content a {
  @apply text-primary hover:underline;
}

.rich-text-content strong {
  @apply font-semibold text-foreground;
}

.rich-text-content em {
  @apply italic;
}

.rich-text-content u {
  @apply underline;
}

.rich-text-content s {
  @apply line-through;
}
