const express = require('express');
const router = express.Router();
const { body, param, query } = require('express-validator');

// Import controllers
const {
  getDailyQuestion,
  submitDailyAnswer,
  getDailyStats,
  getDailyHistory,
  getDailyLeaderboard,
  getTodaysDailyQuestion,
  getDailyQuestionForDate,
  getUserHistory,
  getStatistics,
  getAnalytics,
  createDailyQuestionsRange,
  updateTodaysDailyQuestion
} = require('../controllers/dailyQuestionController');

// Import middleware
const { 
  auth,
  admin,
  premium,
  checkPremiumAccess,
  optionalAuth,
  rateLimitByUser
} = require('../middleware/auth');

const { handleValidationErrors } = require('../validators/authValidators');

// Import validators
const {
  validateSubmitDailyAnswer
} = require('../validators/dailyQuestionValidators');

// Validation for date parameter
const validateDate = [
  param('date')
    .isISO8601()
    .withMessage('Date must be in ISO 8601 format (YYYY-MM-DD)'),
  
  handleValidationErrors
];

// Validation for creating daily questions range
const validateCreateRange = [
  body('startDate')
    .isISO8601()
    .withMessage('Start date must be in ISO 8601 format'),
  
  body('endDate')
    .isISO8601()
    .withMessage('End date must be in ISO 8601 format')
    .custom((endDate, { req }) => {
      if (new Date(endDate) <= new Date(req.body.startDate)) {
        throw new Error('End date must be after start date');
      }
      return true;
    }),
  
  handleValidationErrors
];

// Validation for statistics query
const validateStatsQuery = [
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be in ISO 8601 format'),
  
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be in ISO 8601 format'),
  
  query('tier')
    .optional()
    .isIn(['free', 'premium'])
    .withMessage('Tier must be either free or premium'),
  
  handleValidationErrors
];

// Validation for analytics query
const validateAnalyticsQuery = [
  query('days')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Days must be between 1 and 365'),
  
  handleValidationErrors
];

// Regular user routes
// @route   GET /daily-questions/
// @desc    Get the current/next daily question for the authenticated user
// @access  Private
router.get('/', auth, getDailyQuestion);

// @route   POST /daily-questions/submit
// @desc    Submit an answer for the current daily question
// @access  Private
router.post('/submit', auth, validateSubmitDailyAnswer, submitDailyAnswer);

// @route   GET /daily-questions/stats
// @desc    Get stats for the authenticated user's daily question activity
// @access  Private
router.get('/stats', auth, getDailyStats);

// @route   GET /daily-questions/history
// @desc    Get the authenticated user's history of daily question attempts
// @access  Private
router.get('/history', auth, getDailyHistory);

// @route   GET /daily-questions/leaderboard
// @desc    Get the daily question leaderboard
// @access  Private
router.get('/leaderboard', auth, getDailyLeaderboard);

// Premium user routes
// @route   GET /daily-questions/today
// @desc    Get today's premium daily question (optional auth; premium only)
// @access  Public (premium users only)
router.get('/today', optionalAuth, checkPremiumAccess, getTodaysDailyQuestion);

// @route   POST /daily-questions/today/submit
// @desc    Submit answer for today's premium daily question
// @access  Private (premium users only)
router.post('/today/submit', 
  auth,
  checkPremiumAccess,
  rateLimitByUser(5, 15 * 60 * 1000), // 5 submissions per 15 minutes
  validateSubmitDailyAnswer,
  submitDailyAnswer
);

// @route   GET /daily-questions/date/:date
// @desc    Get the daily question for a specific date (ISO 8601)
// @access  Public (premium users only)
router.get('/date/:date', 
  optionalAuth, 
  checkPremiumAccess, 
  validateDate, 
  getDailyQuestionForDate
);

// Admin routes
// @route   GET /daily-questions/admin/stats
// @desc    Get aggregated statistics for daily questions (admin)
// @access  Private (admin)
router.get('/admin/stats', 
  auth, 
  admin, 
  validateStatsQuery, 
  getStatistics
);

// @route   GET /daily-questions/admin/analytics
// @desc    Get analytics for daily questions over a time window (admin)
// @access  Private (admin)
router.get('/admin/analytics', 
  auth, 
  admin, 
  validateAnalyticsQuery, 
  getAnalytics
);

// @route   POST /daily-questions/admin/create-range
// @desc    Create daily questions for a date range (admin)
// @access  Private (admin)
router.post('/admin/create-range', 
  auth, 
  admin, 
  validateCreateRange, 
  createDailyQuestionsRange
);

// @route   POST /daily-questions/admin/update-today
// @desc    Update today's daily question (admin)
// @access  Private (admin)
router.post('/admin/update-today', 
  auth, 
  admin, 
  updateTodaysDailyQuestion
);

module.exports = router;
