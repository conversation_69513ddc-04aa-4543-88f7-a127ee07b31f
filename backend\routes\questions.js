const express = require('express');
const router = express.Router();
const { param } = require('express-validator');

// Import controllers
const {
  getQuestions,
  getQuestionById,
  createQuestion,
  updateQuestion,
  deleteQuestion,
  getQuestionsByTopic,
  getRandomQuestions,
  bulkDeleteQuestions,
  bulkUpdateQuestions,
  getDailyQuestion
} = require('../controllers/questionController');

// Import middleware
const { auth, admin, optionalAuth } = require('../middleware/auth');

// Import validators
const {
  validateCreateQuestion,
  validateUpdateQuestion,
  validateGetQuestions,
  validateQuestionId,
  validateBulkDelete,
  validateBulkUpdate,
  handleValidationErrors
} = require('../validators/questionValidators');

// Public routes (with optional auth to detect admin users)
// @route   GET /api/questions
// @desc    Get questions
// @access  private
router.get('/', validateGetQuestions, auth, getQuestions);

// @route   GET /api/questions/:id
// @desc    Get question by ID
// @access  private
router.get('/:id', validateQuestionId, auth, getQuestionById);

// @route   GET /api/questions/topic/:topicId
// @desc    Get questions by topic
// @access  private
router.get('/topic/:topicId', param('topicId').isMongoId(), handleValidationErrors, auth, getQuestionsByTopic);

// @route   GET /api/questions/random/:count
// @desc    Get random questions
// @access  Private
router.get('/random/:count', param('count').isInt({ min: 1, max: 50 }), handleValidationErrors, auth, getRandomQuestions);

// Admin routes
// @route   POST /api/questions
// @desc    Create new question
// @access  Private/Admin
router.post('/', auth, admin, validateCreateQuestion, createQuestion);

// @route   PUT /api/questions/:id
// @desc    Update question
// @access  Private/Admin
router.put('/:id', auth, admin, validateQuestionId, validateUpdateQuestion, updateQuestion);

// @route   DELETE /api/questions/:id
// @desc    Delete question
// @access  Private/Admin
router.delete('/:id', auth, admin, validateQuestionId, deleteQuestion);

// @route   POST /api/questions/bulk-delete
// @desc    Bulk delete questions
// @access  Private/Admin
router.post('/bulk-delete', auth, admin, validateBulkDelete, bulkDeleteQuestions);

// @route   PUT /api/questions/bulk-update
// @desc    Bulk update questions
// @access  Private/Admin
router.put('/bulk-update', auth, admin, validateBulkUpdate, bulkUpdateQuestions);

// @route   GET /api/questions/daily
// @desc    Get daily question
// @access  Private
router.get('/daily', auth, getDailyQuestion);

module.exports = router;
