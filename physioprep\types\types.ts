import { JSONContent } from "novel";

export interface Category {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
}

export interface SubjectStats {
  totalQuestions: number;
  freeQuestions: number;
  premiumQuestions: number;
  totalQuizzes: number;
  totalTests: number;
}

export interface QuestionDistribution {
  free: number;
  premium: number;
}

export interface TopicStats {
  totalQuestions: number;
  freeQuestions: number;
  premiumQuestions: number;
  activeQuestions: number;
  inactiveQuestions: number;
  averageDifficulty: number;
  difficultyDistribution: {
    easy: number;
    medium: number;
    hard: number;
  };
  totalAttempts: number;
  averageScore: number;
  completionRate: number;
  popularityScore: number;
  lastActivityDate: string;
}

export interface Topic {
  _id: string;
  topicName: string;
  descriptionJson: string  | JSONContent;
  descriptionHtml: string;
  subject: Subject; // subject _id
  isActive: boolean;
  isPremium: boolean;
  createdAt: string;
  updatedAt: string;
  questionCount?: number; // for UI
  stats?: TopicStats;
}

export interface Subject {
  id: string;
  _id: string;
  name: string;
  description: string;
  slug: string;
  icon: string;
  color: string;
  isActive: boolean;
  order: number;
  categories: Category[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  stats: SubjectStats;
  questionDistribution: QuestionDistribution;
  topics: Topic[]; // populated topics
  __v: number;
}

export interface Quiz {
  _id: string;
  user: string;
  subject: string;
  topic?: string;
  mode: 'subject-quiz' | 'topic-quiz';
  totalQuestions: number;
  questions: QuizQuestion[];
  status: 'pending' | 'in_progress' | 'completed' | 'abandoned';
  score: number;
  startedAt?: string;
  completedAt?: string;
}

export interface QuizQuestion {
  question: Question;
  userAnswer?: number;
  isCorrect?: boolean;
  timeSpent?: number;
  answeredAt?: string;
}

export interface Test {
  _id: string;
  user: string;
  subject?: string;
  topic?: string;
  mode: 'subject-test' | 'topic-test' | 'mixed-test';
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  totalQuestions: number;
  questions: TestQuestion[];
  status: 'pending' | 'in_progress' | 'completed' | 'abandoned';
  score: number;
  startedAt?: string;
  completedAt?: string;
  timeLimit: number;
}

export interface TestQuestion {
  question: Question;
  userAnswer?: number;
  isCorrect?: boolean;
  timeSpent?: number;
}

export interface Question {
  _id: string;
  text: string;
  options: { text: string; isCorrect: boolean }[];
  explanation: string;
  difficulty: 'easy' | 'medium' | 'hard';
  tier: 'free' | 'premium';
  topic: Topic; // topic _id
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
  subject?: Subject; // subject _id
  explanationJson?: string | JSONContent;
  explanationHtml?: string;
  usageCount?: number;
  correctAnswerCount?: number;
  averageTimeSpent?: number;
  lastAttemptDate?: string;
}

export type TopicFormValues = {
  topicName: string;
  description: string;
  isActive?: boolean;
};

export interface QuestionFormValues {
  text: string;
  options: {
    text: string;
    isCorrect: boolean;
  }[];
  explanation: string;
  difficulty: 'easy' | 'medium' | 'hard';
  tier: 'free' | 'premium';
}

// API Response Types
export interface ApiSuccessResponse<T = unknown> {
  success: true;
  data: T;
}

export interface ApiErrorResponse {
  success: false;
  errors: Array<{ msg: string; field?: string }>;
}

export type ApiResponse<T = unknown> = ApiSuccessResponse<T> | ApiErrorResponse;

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}

export interface User {
  id: string;
  _id: string;
  name: string;
  email: string;
  role: 'user' | 'admin';
  isPremium: boolean;
  isPremiumActive: boolean;
  isEmailVerified: boolean;
  preferences: UserPreferences;
  stats: UserStats;
  avatar: string | null;
  premiumExpiry: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  notifications: boolean;
  dailyReminder: boolean;
  theme: 'light' | 'dark' | 'auto';
}

export interface UserStats {
  totalQuizzesTaken: number;
  totalTestsTaken: number;
  totalQuestionsAnswered: number;
  correctAnswers: number;
  averageScore: number;
  currentStreak: number;
  longestStreak: number;
  lastActivityDate: string | null;
}

// Dashboard Types
export interface DashboardProgress {
  totalQuizzes: number;
  totalTests: number;
  averageScore: number;
  recentActivity: ActivityItem[];
}

export interface ActivityItem {
  id: string;
  type: 'quiz' | 'test';
  subject: string;
  score: number;
  date: string;
}

export interface UpcomingQuiz {
  id: string;
  subject: string;
  topic?: string;
  scheduledDate: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface QuickStats {
  todayQuizzes: number;
  weeklyProgress: number;
  currentStreak: number;
  totalPoints: number;
}

// Quiz and Test Types
export interface QuizSubmissionData {
  answers: number[];
  timeSpent?: number;
}

export interface TestSubmissionData {
  answers: number[];
  timeSpent?: number;
}

export interface TestStartData {
  timeLimit: number;
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
}

// Topic API Types
export interface TopicCreateData {
  topicName: string;
  description: string;
  subject: string;
  isActive?: boolean;
  isPremium?: boolean;
}

export interface TopicUpdateData {
  topicName?: string;
  description?: string;
  isActive?: boolean;
  isPremium?: boolean;
}

export interface TopicWithSubjectResponse {
  topic: Topic;
  subject: Subject;
}

export interface TopicDeleteResponse {
  msg: string;
  subject: Subject;
}

// Question API Types
export interface QuestionCreateData {
  text: string;
  options: Array<{ text: string; isCorrect: boolean }>;
  explanation: string;
  difficulty: 'easy' | 'medium' | 'hard';
  tier: 'free' | 'premium';
  topic: string;
}

export interface QuestionUpdateData {
  text?: string;
  options?: Array<{ text: string; isCorrect: boolean }>;
  explanation?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  tier?: 'free' | 'premium';
  isActive?: boolean;
}

export interface QuestionQueryParams {
  page?: number;
  limit?: number;
  difficulty?: 'easy' | 'medium' | 'hard';
  tier?: 'free' | 'premium';
  isActive?: boolean;
}
