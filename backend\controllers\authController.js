const jwt = require("jsonwebtoken"); // Import JWT library
const crypto = require("crypto");
const User = require("../models/User");
const { asyncHandler } = require("../middleware/errorHandler");
const sendEmail = require("../utils/sendEmail");
const bcrypt = require("bcryptjs");
const { validationResult } = require("express-validator");

// Generate access token
const generateAccessToken = (userId) => {
  return jwt.sign(
    {
      userId,
      tokenType: "access",
    },
    process.env.JWT_SECRET,
    {
      expiresIn: "30s", // Shorter expiry for user tokens for security (30 seconds)
    }
  );
};

// Generate refresh token
const generateRefreshToken = (userId) => {
  return jwt.sign(
    {
      userId,
      tokenType: "refresh",
    },
    process.env.JWT_SECRET,
    {
      expiresIn: "7d", // Longer expiry for refresh tokens (7 days)
    }
  );
};

// Verify access token
const verifyAccessToken = (accessToken) => {
  return jwt.verify(accessToken, process.env.JWT_SECRET);
};

// Verify refresh token
const verifyRefreshToken = (refreshToken) => {
  return jwt.verify(refreshToken, process.env.JWT_SECRET);
};

const refreshUserToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({ error: "Refresh token required" });
    }

    // Verify refresh token
    let decoded;
    try {
      decoded = verifyRefreshToken(refreshToken);
    } catch (error) {
      return res.status(401).json({ error: "Invalid refresh token" });
    }

    // Find user and verify refresh token exists in database
    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(401).json({ error: "User not found" });
    }

    const storedRefreshToken = user.refreshTokens.find(
      (rt) => rt.token === refreshToken && rt.expiresAt > new Date()
    );

    if (!storedRefreshToken) {
      return res
        .status(401)
        .json({ error: "Invalid or expired refresh token" });
    }

    // Generate new access token
    const newAccessToken = generateAccessToken(user._id);

    // Optionally, generate new refresh token (refresh token rotation)
    const newRefreshToken = generateRefreshToken(user._id);

    // Remove old refresh token and add new one
    user.refreshTokens = user.refreshTokens.filter(
      (rt) => rt.token !== refreshToken
    );

    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    user.refreshTokens.push({
      token: newRefreshToken,
      expiresAt,
      deviceInfo: req.headers["user-agent"] || "Unknown",
    });

    await user.save();

    res.json({
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
    });
  } catch (error) {
    console.error("Refresh token error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

// Register new user
const register = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }

    const { email, password, name } = req.body;

    // Check if user already exists
    let user = await User.findOne({ email });
    if (user) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "User already exists" }],
      });
    }

    // Create new user
    user = new User({
      email,
      password,
      name,
    });

    await user.save();

    // Generate token
    const token = generateAccessToken(user._id);
    const refreshToken = generateRefreshToken(user._id);

    // Add refresh token to user's refreshTokens array
    user.refreshTokens.push({
      token: refreshToken,
      createdAt: Date.now(),
      expiresAt: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days
      deviceInfo: req.headers["user-agent"], // Optional: track devices
    });

    // Remove expired tokens
    user.refreshTokens = user.refreshTokens.filter(
      (rt) => rt.expiresAt > new Date()
    );

    await user.save();

    const userObj = await User.findById(user._id).select("-password").lean();
    res.status(201).json({
      success: true,
      data: {
        token,
        refreshToken,
        user: {
          id: userObj._id,
          name: userObj.name,
          email: userObj.email,
          role: userObj.role,
          isPremium: userObj.isPremium,
          isPremiumActive: user.isPremiumActive,
          isEmailVerified: userObj.isEmailVerified,
          preferences: userObj.preferences,
          stats: userObj.stats,
          avatar: userObj.avatar,
          premiumExpiry: userObj.premiumExpiry,
          createdAt: userObj.createdAt,
          updatedAt: userObj.updatedAt,
        },
      },
    });
  } catch (error) {
    console.error("Register error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error during registration" }],
    });
  }
};

// Login user
const login = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }

    const { email, password } = req.body;

    // Check if user exists and select password field
    const user = await User.findOne({ email }).select("+password");
    if (!user) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Invalid credentials" }],
      });
    }

    // Check password
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      console.log("Password did not match");
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Invalid credentials" }],
      });
    }

    // Update login stats
    await user.updateLoginStats();

    // Generate token
    const token = generateAccessToken(user._id);
    const refreshToken = generateRefreshToken(user._id);

    // Add refresh token to user's refreshTokens array
    user.refreshTokens.push({
      token: refreshToken,
      createdAt: Date.now(),
      expiresAt: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days
      deviceInfo: req.headers["user-agent"], // Optional: track devices
    });

    // Remove expired tokens
    user.refreshTokens = user.refreshTokens.filter(
      (rt) => rt.expiresAt > new Date()
    );

    await user.save();

    const userObj = await User.findById(user._id).select("-password").lean();
    res.json({
      success: true,
      data: {
        token,
        refreshToken,
        user: {
          id: userObj._id,
          name: userObj.name,
          email: userObj.email,
          role: userObj.role,
          isPremium: userObj.isPremium,
          isPremiumActive: user.isPremiumActive,
          isEmailVerified: userObj.isEmailVerified,
          preferences: userObj.preferences,
          stats: userObj.stats,
          avatar: userObj.avatar,
          premiumExpiry: userObj.premiumExpiry,
          createdAt: userObj.createdAt,
          updatedAt: userObj.updatedAt,
        },
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error during login" }],
    });
  }
};

const logout = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (refreshToken) {
      // Find user by refresh token and remove it
      const decoded = verifyRefreshToken(refreshToken);
      const user = await User.findById(decoded.userId);

      if (user) {
        user.refreshTokens = user.refreshTokens.filter(
          (rt) => rt.token !== refreshToken
        );
        await user.save();
      }
    }

    res.json({ success: true, message: "Logged out successfully" });
  } catch (error) {
    // Even if token verification fails, still return success for logout
    res.json({ success: true, message: "Logged out successfully" });
  }
};

const logoutAll = async (req, res) => {
  try {
    const user = await User.findById(req.user._id);
    if (user) {
      user.refreshTokens = [];
      await user.save();
    }
    res.json({
      success: true,
      message: "All sessions logged out successfully",
    });
  } catch (error) {
    res.status(500).json({ success: false, error: "Server error" });
  }
};

// Get current user
const getCurrentUser = async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select("-password");
    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
          isPremium: user.isPremium,
        },
      },
    });
  } catch (error) {
    console.error("Get current user error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching user data" }],
    });
  }
};

// Update user profile
const updateProfile = asyncHandler(async (req, res) => {
  const { name, preferences } = req.body;

  const updateData = {};
  if (name) updateData.name = name;
  if (preferences)
    updateData.preferences = { ...req.user.preferences, ...preferences };

  const user = await User.findByIdAndUpdate(req.user._id, updateData, {
    new: true,
    runValidators: true,
  });

  res.status(200).json({
    success: true,
    data: {
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        isPremium: user.isPremium,
        isPremiumActive: user.isPremiumActive,
        isEmailVerified: user.isEmailVerified,
        preferences: user.preferences,
        stats: user.stats,
        avatar: user.avatar,
      },
    },
  });
});

// Change password
const changePassword = asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = req.body;

  // Get user with password
  const user = await User.findById(req.user._id).select("+password");

  // Check current password
  const isCurrentPasswordValid = await user.comparePassword(currentPassword);
  if (!isCurrentPasswordValid) {
    return res.status(400).json({
      success: false,
      errors: [{ msg: "Current password is incorrect" }],
    });
  }

  // Update password
  user.password = newPassword;
  await user.save();

  res.status(200).json({
    success: true,
    data: { message: "Password updated successfully" },
  });
});

// Request password reset
const requestPasswordReset = asyncHandler(async (req, res) => {
  const { email } = req.body;

  const user = await User.findOne({ email });
  if (!user) {
    // Don't reveal if email exists or not
    return res.status(200).json({
      success: true,
      data: {
        message:
          "If an account with that email exists, a password reset link has been sent",
      },
    });
  }

  // Generate reset token
  const resetToken = crypto.randomBytes(32).toString("hex");
  user.passwordResetToken = resetToken;
  user.passwordResetExpires = Date.now() + 10 * 60 * 1000; // 10 minutes
  await user.save();

  // Send reset email
  try {
    await sendEmail({
      to: user.email,
      subject: "Reset Your PhysioPrep Password",
      template: "passwordReset",
      data: {
        name: user.name,
        resetUrl: `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`,
      },
    });
  } catch (emailError) {
    console.error("Failed to send password reset email:", emailError);
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save();

    return res.status(500).json({
      success: false,
      errors: [{ msg: "Failed to send password reset email" }],
    });
  }

  res.status(200).json({
    success: true,
    data: { message: "Password reset link sent to your email" },
  });
});

// Reset password
const resetPassword = asyncHandler(async (req, res) => {
  const { token, password } = req.body;

  const user = await User.findOne({
    passwordResetToken: token,
    passwordResetExpires: { $gt: Date.now() },
  });

  if (!user) {
    return res.status(400).json({
      success: false,
      errors: [{ msg: "Invalid or expired reset token" }],
    });
  }

  // Update password
  user.password = password;
  user.passwordResetToken = undefined;
  user.passwordResetExpires = undefined;
  await user.save();

  // Generate new JWT token
  const jwtToken = generateAccessToken(user._id);

  res.status(200).json({
    success: true,
    data: {
      message: "Password reset successfully",
      token: jwtToken,
    },
  });
});

// Verify email
const verifyEmail = asyncHandler(async (req, res) => {
  const { token } = req.body;

  const user = await User.findOne({ emailVerificationToken: token });
  if (!user) {
    return res.status(400).json({
      success: false,
      errors: [{ msg: "Invalid verification token" }],
    });
  }

  user.isEmailVerified = true;
  user.emailVerificationToken = undefined;
  await user.save();

  res.status(200).json({
    success: true,
    data: { message: "Email verified successfully" },
  });
});

// Resend verification email
const resendVerificationEmail = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user._id);

  if (user.isEmailVerified) {
    return res.status(400).json({
      success: false,
      errors: [{ msg: "Email is already verified" }],
    });
  }

  // Generate new verification token
  const emailVerificationToken = crypto.randomBytes(32).toString("hex");
  user.emailVerificationToken = emailVerificationToken;
  await user.save();

  // Send verification email
  try {
    await sendEmail({
      to: user.email,
      subject: "Verify Your PhysioPrep Account",
      template: "emailVerification",
      data: {
        name: user.name,
        verificationUrl: `${process.env.FRONTEND_URL}/verify-email?token=${emailVerificationToken}`,
      },
    });
  } catch (emailError) {
    console.error("Failed to send verification email:", emailError);
    return res.status(500).json({
      success: false,
      errors: [{ msg: "Failed to send verification email" }],
    });
  }

  res.status(200).json({
    success: true,
    data: { message: "Verification email sent" },
  });
});

module.exports = {
  register,
  login,
  getCurrentUser,
  updateProfile,
  changePassword,
  requestPasswordReset,
  resetPassword,
  verifyEmail,
  resendVerificationEmail,
  refreshUserToken,
  logout,
  logoutAll,
};
