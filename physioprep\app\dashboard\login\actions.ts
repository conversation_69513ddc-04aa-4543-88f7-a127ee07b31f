"use server";

import axios, { AxiosError } from "axios";
import { cookies } from "next/headers";
import { getApiUrl, handleServerActionError, getRefreshToken, setAuthCookies, clearAuthCookies } from "@/services/api";

export type LoginState = {
  error: string | null;
  success: boolean;
  message?: string;
};

export type RefreshTokenState = {
  error: string | null;
  success: boolean;
  message?: string;
};

export async function adminLogin(
  previousState: { error: string | null },
  formData: FormData
): Promise<LoginState> {
  const email = formData.get("email");
  const password = formData.get("password");

  if (!email || !password) {
    return { error: "Email and password are required", success: false };
  }

  try {
    const apiUrl = await getApiUrl();
    const response = await axios.post(`${apiUrl}/auth/admin/login`, {
      email: email,
      password: password,
    });
    console.log(response.data);

    if (response.data.success) {
      // Set admin cookies
      const cookieStore = await cookies();
      cookieStore.set("adminToken", response.data.data.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 24 * 60 * 60, // 24 hours
      });

      if (response.data.data.refreshToken) {
        cookieStore.set("adminRefreshToken", response.data.data.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          sameSite: "strict",
          maxAge: 7 * 24 * 60 * 60, // 7 days
        });
      }

      cookieStore.set("isLoggedIn", "true", {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 24 * 60 * 60,
      });

      return { error: null, success: true, message: "Login successful" };
    }
    // If not successful, return error
    return { error: "Login failed", success: false };
  } catch (error) {
    const err = await handleServerActionError(
      error as AxiosError,
      "Failed to login"
    );
    return {
      success: false,
      error: err?.errors?.[0]?.msg || "Login failed",
    };
  }
}

// Admin refresh token server action
export async function adminRefreshToken(): Promise<RefreshTokenState> {
  try {
    const refreshToken = await getRefreshToken("admin");

    if (!refreshToken) {
      await clearAuthCookies(true);
      return {
        error: "No refresh token available. Please login again.",
        success: false
      };
    }

    const apiUrl = await getApiUrl();
    const response = await axios.post(`${apiUrl}/auth/admin/refresh`, {
      refreshToken
    });

    if (response.data.success) {
      // Update cookies with new tokens
      await setAuthCookies(
        response.data.data.token,
        true,
        response.data.data.refreshToken
      );

      return {
        error: null,
        success: true,
        message: "Token refreshed successfully"
      };
    }

    // If refresh failed, clear cookies
    await clearAuthCookies(true);
    return {
      error: response.data.errors?.[0]?.msg || "Failed to refresh token",
      success: false
    };
  } catch (error) {
    console.error("Admin token refresh error:", error);

    // Clear cookies on any error
    await clearAuthCookies(true);

    const err = await handleServerActionError(
      error as AxiosError,
      "Failed to refresh token"
    );

    return {
      success: false,
      error: err?.errors?.[0]?.msg || "Token refresh failed. Please login again.",
    };
  }
}

// Admin logout server action
export async function adminLogout(): Promise<LoginState> {
  try {
    const apiUrl = await getApiUrl();

    // Try to call logout endpoint (optional, since we're clearing cookies anyway)
    try {
      await axios.post(`${apiUrl}/auth/admin/logout`);
    } catch (error) {
      // Ignore logout endpoint errors, just clear cookies
      console.warn("Logout endpoint error (ignored):", error);
    }

    // Clear authentication cookies
    await clearAuthCookies(true);

    return {
      error: null,
      success: true,
      message: "Logged out successfully"
    };
  } catch {
    // Even if there's an error, clear cookies
    await clearAuthCookies(true);

    return {
      success: true, // Still return success since cookies are cleared
      error: null,
      message: "Logged out successfully"
    };
  }
}
