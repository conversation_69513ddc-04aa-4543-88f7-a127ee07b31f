"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { DataTablePagination } from "./DataTablePagination";
import { RefreshCw, Search } from "lucide-react";
import DataTable from "./DataTable";


interface FilterOption {
  label: string;
  value: string;
}

interface FilterConfig {
  name: string;
  label: string;
  options: FilterOption[];
  value: string | string[];
  onChange: (value: string | string[]) => void;
}

interface EnhancedDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  searchPlaceholder?: string;
  searchColumn?: string;
  enablePagination?: boolean; // New prop to control pagination
  pagination?: {
    pageIndex: number;
    pageSize: number;
    pageCount: number;
    totalItems: number;
  };
  onPaginationChange?: (pagination: {
    pageIndex: number;
    pageSize: number;
  }) => void;
  onSearch?: (value: string) => void;
  onRefresh?: () => void;
  isLoading?: boolean;
  filters?: FilterConfig[];
  additionalFilters?: React.ReactNode;
  enableRowSelection?: boolean; // New prop to control row selection
  onSelectionChange?: (selectedRows: TData[]) => void; // Callback for selected rows
}

export function EnhancedDataTable<TData, TValue>({
  columns,
  data,
  searchPlaceholder = "Search...",
  searchColumn,
  enablePagination = true, // Default to true for backward compatibility
  pagination,
  onPaginationChange,
  onSearch,
  onRefresh,
  isLoading = false,
  filters = [],
  additionalFilters,
  enableRowSelection = false,
  onSelectionChange,
}: EnhancedDataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [searchValue, setSearchValue] = React.useState("");
  const [globalFilter, setGlobalFilter] = React.useState("");

  // Set up local pagination if server pagination is not provided and pagination is enabled
  const paginationState =
    enablePagination && pagination
      ? {
          pageIndex: pagination.pageIndex - 1, // Convert 1-based to 0-based
          pageSize: pagination.pageSize,
        }
      : undefined;

  const table = useReactTable<TData>({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
      ...(paginationState ? { pagination: paginationState } : {}),
    },
    enableRowSelection,
    enableGlobalFilter: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    ...(enablePagination
      ? paginationState
        ? {} // Server-side pagination
        : { getPaginationRowModel: getPaginationRowModel() } // Client-side pagination
      : {}), // No pagination
    manualPagination: enablePagination && !!pagination, // Enable manual pagination if server pagination is provided and pagination is enabled
    pageCount: enablePagination ? pagination?.pageCount || -1 : -1,
  });

  // Optional: callback with selected rows
  React.useEffect(() => {
    if (onSelectionChange) {
      const selectedRows = table.getSelectedRowModel().rows.map(
        (r) => r.original
      );
      onSelectionChange(selectedRows);
    }
  }, [rowSelection, onSelectionChange, table]);

  // Handle page change
  const handlePageChange = (page: number) => {
    if (onPaginationChange) {
      onPaginationChange({
        pageIndex: page,
        pageSize: pagination?.pageSize || table.getState().pagination.pageSize,
      });
    }
  };

  // Handle page size change
  const handlePageSizeChange = (pageSize: number) => {
    if (onPaginationChange) {
      onPaginationChange({
        pageIndex: 1, // Reset to first page when changing page size
        pageSize,
      });
    }
  };

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);

    // If external search function is provided, use it
    if (onSearch) {
      // Debounce external search
      onSearch(value);
    } else {
      console.log('u')
      // Use internal search filtering
      if (searchColumn) {
        // Filter by specific column
        table.getColumn(searchColumn)?.setFilterValue(value);
      } else {
        // Global filter - search across all columns
        setGlobalFilter(value);
      }
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={searchPlaceholder}
            value={searchValue}
            onChange={handleSearch}
            className="pl-8"
          />
        </div>

        {/* Filters */}
        {filters.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {filters.map((filter) => (
              <div key={filter.name} className="flex items-center gap-2">
                <label
                  htmlFor={filter.name}
                  className="text-sm font-medium whitespace-nowrap"
                >
                  {filter.label}:
                </label>

                <select
                  id={filter.name}
                  value={
                    Array.isArray(filter.value)
                      ? filter.value[0] || ""
                      : filter.value
                  }
                  onChange={(e) => filter.onChange(e.target.value)}
                  className="h-9 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
                >
                  {filter.options.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            ))}
          </div>
        )}

        {onRefresh && (
          <Button variant="outline" onClick={onRefresh} disabled={isLoading}>
            <RefreshCw
              className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
        )}

        {/* Additional Filters */}
        {additionalFilters && <div>{additionalFilters}</div>}
      </div>

      <DataTable
        table={table}
        isLoading={isLoading}
        enableRowSelection={enableRowSelection}
      />

      {enablePagination && (
        <DataTablePagination
          table={table}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          totalPages={pagination?.pageCount}
          totalItems={pagination?.totalItems}
        />
      )}
    </div>
  );
}
