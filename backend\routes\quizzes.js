const express = require("express");
const router = express.Router();
const { body, param } = require("express-validator");

// Import controllers
const {
  submitQuiz,
  startTopicQuiz,
  startSubjectQuiz,
} = require("../controllers/quizController");

// Import middleware
const {
  auth,
  admin,
  premium,
  checkPremiumAccess,
} = require("../middleware/auth");

const {
  validateCreateQuiz,
  validateUpdateQuiz,
  validateSubmitQuiz,
} = require("../validators/quizValidators");

// Submit a quiz
// @route   POST /api/quizzes/submit

router.post(
  "/submit",
  auth,
  validateSubmitQuiz,
  submitQuiz
);

// Start a subject-wise quiz for a user
// @route   POST /api/quizzes/subject/:subjectId/start
router.post(
  "/subject/:subjectId/start",
  auth,
  param("subjectId").isMongoId(),
  startSubjectQuiz
);
// Start a topic-wise quiz for a user
// @route   POST /api/quizzes/topic/:topicId/start
router.post(
  "/topic/:topicId/start",
  auth,
  param("topicId").isMongoId(),
  startTopicQuiz
);

module.exports = router;
