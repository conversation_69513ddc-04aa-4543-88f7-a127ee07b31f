import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { AlignLeft, AlignCenter, AlignRight, AlignJustify, ChevronDown } from "lucide-react";
import { EditorBubbleItem, useEditor } from "novel";

interface AlignmentSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const alignmentOptions = [
  {
    name: "Left",
    value: "left",
    icon: AlignLeft,
    description: "Align text to the left",
  },
  {
    name: "Center",
    value: "center",
    icon: AlignCenter,
    description: "Center align text",
  },
  {
    name: "Right",
    value: "right",
    icon: AlignRight,
    description: "Align text to the right",
  },
  {
    name: "Justify",
    value: "justify",
    icon: AlignJustify,
    description: "Justify text alignment",
  },
];

export const AlignmentSelector = ({ open, onOpenChange }: AlignmentSelectorProps) => {
  const { editor } = useEditor();

  if (!editor) return null;

  const getCurrentAlignment = () => {
    if (editor.isActive({ textAlign: "left" })) return "left";
    if (editor.isActive({ textAlign: "center" })) return "center";
    if (editor.isActive({ textAlign: "right" })) return "right";
    if (editor.isActive({ textAlign: "justify" })) return "justify";
    return "left"; // default
  };

  const currentAlignment = getCurrentAlignment();
  const currentOption = alignmentOptions.find(option => option.value === currentAlignment) || alignmentOptions[0];

  return (
    <Popover modal={true} open={open} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>
        <Button variant="ghost" className="gap-2 rounded-none border-none">
          <currentOption.icon className="h-4 w-4" />
          <span className="whitespace-nowrap text-sm">{currentOption.name}</span>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent sideOffset={5} align="start" className="w-48 p-1">
        <div className="flex flex-col gap-1">
          {alignmentOptions.map((option) => (
            <EditorBubbleItem
              key={option.value}
              onSelect={() => {
                editor.chain().focus().setTextAlign(option.value).run();
                onOpenChange(false);
              }}
              className={cn(
                "flex cursor-pointer items-center justify-between rounded-sm px-2 py-1.5 text-sm hover:bg-accent",
                {
                  "bg-accent": currentAlignment === option.value,
                }
              )}
            >
              <div className="flex items-center gap-2">
                <option.icon className="h-4 w-4" />
                <span>{option.name}</span>
              </div>
            </EditorBubbleItem>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};
