const Question = require('../models/Question');
const DailyQuestion = require('../models/DailyQuestion');
const { format, subDays } = require('date-fns');

/**
 * Update daily question for today
 * This function is called by the cron job
 */
const updateDailyQuestion = async () => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    console.log(`🔄 Updating daily question for ${format(today, 'yyyy-MM-dd')}`);

    // Check if today's question already exists
    const existingDailyQuestion = await DailyQuestion.findOne({ 
      date: today,
      isActive: true 
    });

    if (existingDailyQuestion) {
      console.log('✅ Daily question already exists for today');
      return existingDailyQuestion;
    }

    // Get questions used in the last 30 days to avoid repetition
    const thirtyDaysAgo = subDays(today, 30);
    const recentDailyQuestions = await DailyQuestion.find({
      date: { $gte: thirtyDaysAgo },
      isActive: true
    }).select('question');

    const recentQuestionIds = recentDailyQuestions.map(dq => dq.question);

    // Determine tier for today's question (70% free, 30% premium)
    const tier = Math.random() < 0.7 ? 'free' : 'premium';

    // Get a random question that hasn't been used recently
    const availableQuestions = await Question.getRandomForDaily(recentQuestionIds, tier);

    if (!availableQuestions || availableQuestions.length === 0) {
      // If no questions available with tier restriction, try without tier restriction
      const fallbackQuestions = await Question.getRandomForDaily(recentQuestionIds);
      
      if (!fallbackQuestions || fallbackQuestions.length === 0) {
        console.error('❌ No questions available for daily question');
        return null;
      }
      
      availableQuestions.push(fallbackQuestions[0]);
    }

    const selectedQuestion = availableQuestions[0];

    // Create daily question
    const dailyQuestion = await DailyQuestion.createDailyQuestion(
      selectedQuestion._id,
      selectedQuestion.tier,
      today
    );

    // Update question's lastUsedInDaily field
    await Question.findByIdAndUpdate(selectedQuestion._id, {
      lastUsedInDaily: today
    });

    console.log(`✅ Daily question created: ${selectedQuestion.text.substring(0, 50)}...`);
    console.log(`📊 Tier: ${selectedQuestion.tier}, Difficulty: ${selectedQuestion.difficulty}`);

    return dailyQuestion;

  } catch (error) {
    console.error('❌ Error updating daily question:', error);
    throw error;
  }
};

/**
 * Get today's daily question
 */
const getTodaysQuestion = async () => {
  try {
    let dailyQuestion = await DailyQuestion.getTodaysQuestion();

    // If no question exists for today, create one
    if (!dailyQuestion) {
      dailyQuestion = await updateDailyQuestion();
    }

    return dailyQuestion;
  } catch (error) {
    console.error('❌ Error getting today\'s question:', error);
    throw error;
  }
};

/**
 * Get daily question for a specific date
 */
const getQuestionForDate = async (date) => {
  try {
    return await DailyQuestion.getQuestionForDate(date);
  } catch (error) {
    console.error('❌ Error getting question for date:', error);
    throw error;
  }
};

/**
 * Submit answer for today's daily question
 */
const submitDailyAnswer = async (userId, selectedOptionId, timeSpent = 0) => {
  try {
    const dailyQuestion = await getTodaysQuestion();

    if (!dailyQuestion) {
      throw new Error('No daily question available');
    }

    // Check if user has already answered
    if (dailyQuestion.hasUserAnswered(userId)) {
      throw new Error('You have already answered today\'s question');
    }

    // Submit the answer
    await dailyQuestion.submitAnswer(userId, selectedOptionId, timeSpent);

    // Return the updated daily question with user's answer
    return await DailyQuestion.findById(dailyQuestion._id)
      .populate('question')
      .lean();

  } catch (error) {
    console.error('❌ Error submitting daily answer:', error);
    throw error;
  }
};

/**
 * Get user's daily question history
 */
const getUserDailyHistory = async (userId, options = {}) => {
  try {
    return await DailyQuestion.getUserHistory(userId, options);
  } catch (error) {
    console.error('❌ Error getting user daily history:', error);
    throw error;
  }
};

/**
 * Get daily question statistics
 */
const getDailyQuestionStats = async (options = {}) => {
  try {
    return await DailyQuestion.getStatistics(options);
  } catch (error) {
    console.error('❌ Error getting daily question stats:', error);
    throw error;
  }
};

/**
 * Create daily questions for multiple days (admin function)
 */
const createDailyQuestionsForRange = async (startDate, endDate) => {
  try {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const createdQuestions = [];

    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      const currentDate = new Date(date);
      currentDate.setHours(0, 0, 0, 0);

      // Check if question already exists for this date
      const existing = await DailyQuestion.findOne({ date: currentDate });
      if (existing) {
        console.log(`⏭️ Skipping ${format(currentDate, 'yyyy-MM-dd')} - already exists`);
        continue;
      }

      // Get questions used in the last 30 days
      const thirtyDaysAgo = subDays(currentDate, 30);
      const recentDailyQuestions = await DailyQuestion.find({
        date: { $gte: thirtyDaysAgo, $lt: currentDate },
        isActive: true
      }).select('question');

      const recentQuestionIds = recentDailyQuestions.map(dq => dq.question);

      // Determine tier (70% free, 30% premium)
      const tier = Math.random() < 0.7 ? 'free' : 'premium';

      // Get random question
      const availableQuestions = await Question.getRandomForDaily(recentQuestionIds, tier);

      if (availableQuestions && availableQuestions.length > 0) {
        const selectedQuestion = availableQuestions[0];
        
        const dailyQuestion = await DailyQuestion.createDailyQuestion(
          selectedQuestion._id,
          selectedQuestion.tier,
          currentDate
        );

        await Question.findByIdAndUpdate(selectedQuestion._id, {
          lastUsedInDaily: currentDate
        });

        createdQuestions.push({
          date: format(currentDate, 'yyyy-MM-dd'),
          questionId: selectedQuestion._id,
          tier: selectedQuestion.tier
        });

        console.log(`✅ Created daily question for ${format(currentDate, 'yyyy-MM-dd')}`);
      } else {
        console.log(`⚠️ No available questions for ${format(currentDate, 'yyyy-MM-dd')}`);
      }
    }

    return createdQuestions;
  } catch (error) {
    console.error('❌ Error creating daily questions for range:', error);
    throw error;
  }
};

/**
 * Get daily question analytics
 */
const getDailyQuestionAnalytics = async (days = 30) => {
  try {
    const endDate = new Date();
    const startDate = subDays(endDate, days);

    const analytics = await DailyQuestion.aggregate([
      {
        $match: {
          date: { $gte: startDate, $lte: endDate },
          isActive: true
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$date' } },
            tier: '$tier'
          },
          totalAttempts: { $sum: '$stats.totalAttempts' },
          correctAttempts: { $sum: '$stats.correctAttempts' },
          uniqueUsers: { $sum: '$stats.uniqueUsers' }
        }
      },
      {
        $group: {
          _id: '$_id.date',
          tiers: {
            $push: {
              tier: '$_id.tier',
              totalAttempts: '$totalAttempts',
              correctAttempts: '$correctAttempts',
              uniqueUsers: '$uniqueUsers',
              accuracy: {
                $cond: [
                  { $eq: ['$totalAttempts', 0] },
                  0,
                  { $multiply: [{ $divide: ['$correctAttempts', '$totalAttempts'] }, 100] }
                ]
              }
            }
          }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    return analytics;
  } catch (error) {
    console.error('❌ Error getting daily question analytics:', error);
    throw error;
  }
};

module.exports = {
  updateDailyQuestion,
  getTodaysQuestion,
  getQuestionForDate,
  submitDailyAnswer,
  getUserDailyHistory,
  getDailyQuestionStats,
  createDailyQuestionsForRange,
  getDailyQuestionAnalytics
};
