"use client";

import { useState, useTransition, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useAtom } from "jotai";

import {
  deleteQuestion,
  bulkDeleteQuestions,
  getQuestions,
  QuestionFilters as QF,
} from "@/actions/questions";
import { Question } from "@/types/types";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { EnhancedDataTable } from "@/components/datatable/EnhancedDataTable";
import { createQuestionColumns } from "@/components/datatable/columns/QuestionsColumns";
import { questionsAtom, selectedQuestionsAtom } from "@/store/questions";
import { Trash2 } from "lucide-react";
import { QuestionFilters } from "./QuestionFilters";

// Local filter state type (non-optional strings)
type LocalFilters = {
  search: string;
  subject: string;
  topic: string;
  difficulty: string;
  tier: string;
  isActive: string;
};

interface QuestionDataTableProps {
  questions: Question[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalQuestions: number;
    limit: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  onFiltersChange?: (filters: QF) => void;
  onPageChange?: (page: number) => void;
  onSearch?: (search: string) => void;
  isLoading?: boolean;
}

export function QuestionDataTable({
 
  questions: initialQuestions,
  pagination: initialPagination,
}: QuestionDataTableProps) {
  const router = useRouter();
  const [questions, setQuestions] = useAtom(questionsAtom);
  const [selectedQuestions, setSelectedQuestions] = useAtom(
    selectedQuestionsAtom
  );
  const [isLoading, setIsLoading] = useState(false);
  const [pagination, setPagination] = useState<typeof initialPagination>();
  const [filters, setFilters] = useState<LocalFilters>({
    search: "",
    subject: "",
    topic: "",
    difficulty: "",
    tier: "",
    isActive: "",
  });

  const fetchData = async (apiFilters: QF) => {
    setIsLoading(true);
    try {
      console.log("Fetching data with filters:", apiFilters);
      const result = await getQuestions(apiFilters);
      if (result.success) {
        setQuestions(result.data.questions);
        setPagination(result.data.pagination);
      } else {
        toast.error(result.errors[0]?.msg || "Failed to load questions");
      }
    } catch (err) {
      console.error("Error loading questions page:", err);
      toast.error("Failed to load questions");
    }
    finally {
      setIsLoading(false);
    }
  };

  // useEffect(() => {
  //   fetchData();
  // }, [filters, pagination]);

  useEffect(() => {
    setQuestions(initialQuestions);
    setPagination(initialPagination);
  }, [initialQuestions, initialPagination, setQuestions]);

  // Convert LocalFilters to QF and handle filter changes
  const handleFilterChange = (newFilters: LocalFilters) => {
    setFilters(newFilters);
    // Convert to QF format for API call
    const apiFilters: QF = {
      search: newFilters.search || undefined,
      subject: newFilters.subject || undefined,
      topic: newFilters.topic || undefined,
      difficulty: newFilters.difficulty || undefined,
      tier: newFilters.tier || undefined,
      isActive: newFilters.isActive || undefined,
      page: 1, // Reset to first page when filters change
    };
    fetchData(apiFilters);
  };

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false);
  const [questionToDelete, setQuestionToDelete] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();

  const handleDeleteClick = (id: string) => {
    setQuestionToDelete(id);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (!questionToDelete) return;

    startTransition(async () => {
      try {
        const result = await deleteQuestion(questionToDelete);
        if (result.success) {
          setQuestions((prev) =>
            prev.filter((question) => question._id !== questionToDelete)
          );
          toast.success("Question deleted successfully");
          router.refresh();
        } else {
          toast.error(result.errors[0]?.msg || "Failed to delete question");
        }
      } catch {
        toast.error("An unexpected error occurred");
      } finally {
        setDeleteDialogOpen(false);
        setQuestionToDelete(null);
      }
    });
  };

  const handleBulkDelete = () => {
    if (selectedQuestions.length === 0) return;
    setBulkDeleteDialogOpen(true);
  };

  const handleBulkDeleteConfirm = () => {
    if (selectedQuestions.length === 0) return;

    startTransition(async () => {
      try {
        const result = await bulkDeleteQuestions(selectedQuestions);
        if (result.success) {
          setQuestions((prev) =>
            prev.filter((question) => !selectedQuestions.includes(question._id))
          );
          setSelectedQuestions([]);
          toast.success(
            `${selectedQuestions.length} questions deleted successfully`
          );
          router.refresh();
        } else {
          toast.error(result.errors[0]?.msg || "Failed to delete questions");
        }
      } catch {
        toast.error("An unexpected error occurred");
      } finally {
        setBulkDeleteDialogOpen(false);
      }
    });
  };

  const handleSelectionChange = useMemo(() => {
    return (selectedRows: Question[]) => {
      const selectedIds = selectedRows.map((row) => row._id);
      setSelectedQuestions(selectedIds);
    };
  }, [setSelectedQuestions]);

  const columns = createQuestionColumns(handleDeleteClick);

  // Remove this conditional rendering - let EnhancedDataTable handle loading state

  return (
    <>
      <div className="space-y-4">
        {selectedQuestions.length > 0 && (
          <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
            <span className="text-sm font-medium">
              {selectedQuestions.length} question(s) selected
            </span>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleBulkDelete}
              disabled={isPending}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Selected
            </Button>
          </div>
        )}

        <EnhancedDataTable
          isLoading={isLoading}
          columns={columns}
          data={questions}
          searchPlaceholder="Search questions by text, options..."
          enablePagination={true}
          enableRowSelection={true}
          onSearch={(search) => {
            const newFilters = { ...filters, search };
            setFilters(newFilters);
            const apiFilters: QF = {
              search: search || undefined,
              subject: newFilters.subject || undefined,
              topic: newFilters.topic || undefined,
              difficulty: newFilters.difficulty || undefined,
              tier: newFilters.tier || undefined,
              isActive: newFilters.isActive || undefined,
              page: 1,
            };
            fetchData(apiFilters);
          }}
          pagination={{
            pageIndex: pagination?.currentPage || 0,
            pageSize: pagination?.limit || initialPagination.limit,
            pageCount: pagination?.totalPages || initialPagination.totalPages,
            totalItems:
              pagination?.totalQuestions || initialPagination.totalQuestions,
          }}
          onPaginationChange={(pagination) => {
            console.log("page", pagination);
            setPagination({
              ...initialPagination,
              currentPage: pagination.pageIndex,
              limit: pagination.pageSize,
            });
            const apiFilters: QF = {
              page: pagination.pageIndex,
              limit: pagination.pageSize,
              search: filters.search || undefined,
              subject: filters.subject || undefined,
              topic: filters.topic || undefined,
              difficulty: filters.difficulty || undefined,
              tier: filters.tier || undefined,
              isActive: filters.isActive || undefined,
            };
            fetchData(apiFilters);
          }}
          onSelectionChange={handleSelectionChange}
          additionalFilters={
            <QuestionFilters
              filters={filters}
              onFilterChange={handleFilterChange}
            />
          }
        />
      </div>

      {/* Delete Single Question Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              question and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isPending}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              {isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Delete Dialog */}
      <AlertDialog
        open={bulkDeleteDialogOpen}
        onOpenChange={setBulkDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Delete {selectedQuestions.length} questions?
            </AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              selected questions and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isPending}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDeleteConfirm}
              disabled={isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              {isPending
                ? "Deleting..."
                : `Delete ${selectedQuestions.length} Questions`}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
