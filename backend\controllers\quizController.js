const { validationResult } = require("express-validator");
const { asyncHandler } = require("../middleware/errorHandler");
const Quiz = require("../models/Quiz");
const Question = require("../models/Question");
const User = require("../models/User");
const Subject = require("../models/Subject");
const Topic = require("../models/Topic");

// @desc    Start a subject-wise quiz for a user
// @route   POST /api/quizzes/subject/:subjectId/start
// @access  Private
const startSubjectQuiz = asyncHandler(async (req, res) => {
  const subjectId = req.params.subjectId;
  const userId = req.user._id;

  // Find the subject with its topics
  const subject = await Subject.findById(subjectId).populate("topics");
  if (!subject) {
    return res
      .status(404)
      .json({ success: false, errors: [{ msg: "Subject not found" }] });
  }

  // Get user with question attempts to track correctly answered questions
  const user = await User.findById(userId).select("questionAttempts");

  // Get all correctly answered question IDs
  const correctlyAnsweredIds = user.getCorrectlyAnsweredQuestions();

  console.log(
    `User has correctly answered ${correctlyAnsweredIds.length} questions`
  );

  let selectedQuestions = [];

  // Strategy 1: Try to get at least one question per topic (that hasn't been correctly answered)
  for (const topic of subject.topics) {
    const question = await Question.findOne({
      topic: topic._id,
      isActive: true,
      _id: { $nin: correctlyAnsweredIds },
    });

    if (question) {
      selectedQuestions.push(question);
    }
  }

  // Strategy 2: If we need more questions, get random ones from all topics in the subject
  if (selectedQuestions.length < 10) {
    const additionalQuestions = await Question.find({
      subject: subjectId,
      isActive: true,
      _id: {
        $nin: [...correctlyAnsweredIds, ...selectedQuestions.map((q) => q._id)],
      },
    });

    console.log(
      `Found ${additionalQuestions.length} additional questions for subject ${subjectId}`
    );

    // Shuffle and take what we need
    const shuffled = additionalQuestions.sort(() => 0.5 - Math.random());
    const needed = 10 - selectedQuestions.length;
    selectedQuestions = selectedQuestions.concat(shuffled.slice(0, needed));
  }

  // Check if we have enough questions
  if (selectedQuestions.length < 1) {
    return res.status(400).json({
      success: false,
      errors: [
        {
          msg: "Not enough new questions available for this subject. You may have answered all available questions correctly!",
        },
      ],
    });
  }

  // Limit to maximum 10 questions and shuffle final selection
  selectedQuestions = selectedQuestions
    .slice(0, 10)
    .sort(() => 0.5 - Math.random());
  const correctAnswers = selectedQuestions.map((q) =>
    Array.isArray(q.options)
      ? q.options.findIndex((o) => o.isCorrect)
      : -1
  );
  // console.log(JSON.stringify(selectedQuestions, null, 2));
  // console.log("Correct answers:", correctAnswers);

  // Remove correct answers from questions before sending to frontend
  // const questionsForFrontend = selectedQuestions.map(question => {
  //   const questionObj = question.toObject();

  //   // Remove correct answer field (adjust field name based on your Question model)
  //   delete questionObj.correctAnswer;
  //   delete questionObj.correctOptionIndex;

  //   // If options contain isCorrect field, remove it
  //   if (questionObj.options && Array.isArray(questionObj.options)) {
  //     questionObj.options = questionObj.options.map(option => {
  //       const optionObj = { ...option };
  //       delete optionObj.isCorrect;
  //       return optionObj;
  //     });
  //   }

  //   return questionObj;
  // });

  // Create a temporary quiz session object (not saved to DB)
  const quizSession = {
    sessionId: new Date().getTime() + "_" + userId, // Simple session ID
    subject: {
      _id: subject._id,
      name: subject.name,
    },
    totalQuestions: selectedQuestions.length,
    questions: selectedQuestions,
    correctAnswers: correctAnswers,
    createdAt: new Date(),
    timeLimit: 10 * 60 * 1000, // 10 minutes in milliseconds
    mode: "subject-quiz",
  };

  res.status(200).json({
    success: true,
    data: quizSession,
    message: `Quiz generated with ${selectedQuestions.length} questions`,
  });
});

// @desc    Submit quiz
// @route   POST /api/quizzes/submit
// @access  Private
// Function to submit quiz answers and record performance
const submitQuiz = asyncHandler(async (req, res) => {
  console.log("Submitting quiz...");
  const { answers, sessionId } = req.body;
  // answers should be array of { questionId, selectedAnswer }
  const userId = req.user._id;

  if (!answers || !Array.isArray(answers)) {
    return res.status(400).json({
      success: false,
      errors: [{ msg: "Answers array is required" }],
    });
  }

  try {
    const user = await User.findById(userId);
    let correctCount = 0;
    let totalQuestions = answers.length;

    console.log("Answers:", answers);

    // Process each answer
    for (const answer of answers) {
      console.log(answer);
      // Get the question to check correct answer
      const question = await Question.findById(answer.questionId);
      if (!question) continue;

      // Check if answer is correct (adjust logic based on your Question model)
      const isCorrect = checkIfAnswerCorrect(question, answer.selectedAnswer);

      // Record the attempt
      await user.recordQuestionAttempt(answer.questionId, isCorrect);

      if (isCorrect) correctCount++;
    }

    // Calculate score
    const score = Math.round((correctCount / totalQuestions) * 100);

    // Update user stats
    user.stats.totalQuestionsAnswered += totalQuestions;
    user.stats.correctAnswers += correctCount;
    user.stats.totalQuizzesTaken += 1;

    // Recalculate average score
    const totalScore =
      user.stats.averageScore * (user.stats.totalQuizzesTaken - 1) + score;
    user.stats.averageScore = Math.round(
      totalScore / user.stats.totalQuizzesTaken
    );

    await user.save();

    // Update activity streak
    await user.updateStreak();

    res.status(200).json({
      success: true,
      data: {
        score,
        correctAnswers: correctCount,
        totalQuestions,
        percentage: score,
      },
      message: "Quiz completed successfully!",
    });
  } catch (error) {
    console.error("Error submitting quiz:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Failed to submit quiz answers" }],
    });
  }
});

// Helper function to check if answer is correct
// Adjust this based on your Question model structure
const checkIfAnswerCorrect = (question, selectedAnswer) => {
  // If your question has correctOptionIndex field
  if (question.correctOptionIndex !== undefined) {
    return question.correctOptionIndex === selectedAnswer;
  }

  // If your question has correctAnswer field with text
  if (question.correctAnswer) {
    return question.correctAnswer === selectedAnswer;
  }

  // If options have isCorrect field
  if (question.options && question.options[selectedAnswer]) {
    return question.options[selectedAnswer].isCorrect === true;
  }

  return false;
};

// @desc    Start a topic-wise quiz for a user
// @route   POST /api/quizzes/topic/:topicId/start
// @access  Private
const startTopicQuiz = asyncHandler(async (req, res) => {
  const topicId = req.params.topicId;
  const userId = req.user._id;

  // Get user with question attempts to track correctly answered questions
  const user = await User.findById(userId).select("questionAttempts");

  // Get all correctly answered question IDs
  const correctlyAnsweredIds = user.getCorrectlyAnsweredQuestions();

  console.log(
    `User has correctly answered ${correctlyAnsweredIds.length} questions`
  );

  // Get questions for the topic that haven't been correctly answered
  const questions = await Question.find({
    topic: topicId,
    isActive: true,
    _id: { $nin: correctlyAnsweredIds },
    // tier: req.user.isPremiumActive() ? { $in: ["free", "premium"] } : "free",
  });

  // Shuffle and select up to 10 random questions
  const shuffledQuestions = questions.sort(() => 0.5 - Math.random());
  // Filter out any question that is in correctlyAnsweredIds (extra safety)
  const selectedQuestions = shuffledQuestions
    .filter(q => !correctlyAnsweredIds.includes(q._id.toString()))
    .slice(0, 10);

  if (selectedQuestions.length < 1) {
    return res.status(400).json({
      success: false,
      errors: [
        {
          msg: "Not enough new questions available for this topic. You may have answered all available questions correctly!",
        },
      ],
    });
  }

  const correctAnswers = selectedQuestions.map((q) =>
    Array.isArray(q.options)
      ? q.options.findIndex((o) => o.isCorrect)
      : -1
  );

  const quizSession = {
    sessionId: new Date().getTime() + "_" + userId, // Simple session ID
    subject: {
      _id: selectedQuestions[0].subject._id,
      name: selectedQuestions[0].subject.name,
    },
    topic: {
      _id: selectedQuestions[0].topic._id,
      name: selectedQuestions[0].topic.name,
    },
    totalQuestions: selectedQuestions.length,
    questions: selectedQuestions,
    correctAnswers: correctAnswers,
    createdAt: new Date(),
    timeLimit: 10 * 60 * 1000, // 10 minutes in milliseconds
    mode: "topic-quiz",
  };
  res.status(200).json({
    success: true,
    data: quizSession,
    message: `Quiz generated with ${selectedQuestions.length} questions`,
  });

  
});

module.exports = {
  submitQuiz,
  startSubjectQuiz,
  startTopicQuiz,
};
