import { Question } from "@/types/types";
import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";

// Base atoms
const questionAtom = atom<Question | null>(null);
const questionsAtom = atomWithStorage<Question[]>("questions", []);
// Selected questions for bulk operations
const selectedQuestionsAtom = atom<string[]>([]);

const getQuestionByIdAtom = atom(null, (get, set, id: string) => {
  const questions = get(questionsAtom);
  const question = questions.find((question) => question._id === id) ?? null;
  set(questionAtom, question);
  return question;
});

export {
  // Base atoms
  questionAtom,
  questionsAtom,
  selectedQuestionsAtom,
  getQuestionByIdAtom,
};
