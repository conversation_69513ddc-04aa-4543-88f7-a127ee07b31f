// Add this to your extensions array or create a separate extension file

import { Extension } from "@tiptap/core";
import { <PERSON>lug<PERSON>, Plugin<PERSON>ey } from "@tiptap/pm/state";
import { EditorView } from "@tiptap/pm/view";
import { Editor } from "@tiptap/core";

// Declare the commands in the module interface
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    tableCopyPaste: {
      /**
       * Copy table as HTML to clipboard
       */
      copyTableAsHTML: () => ReturnType
      /**
       * Paste table from clipboard
       */
      pasteTableFromClipboard: () => ReturnType
    }
  }
}

// Helper functions defined outside the extension
const isTabularData = (text: string): boolean => {
  const lines = text.trim().split('\n');
  if (lines.length < 2) return false;

  // Check if it has consistent delimiters (tabs or commas)
  const firstLine = lines[0];
  const hasTab = firstLine.includes('\t');
  const hasComma = firstLine.includes(',');
  
  if (!hasTab && !hasComma) return false;
  
  const delimiter = hasTab ? '\t' : ',';
  const firstLineDelimiters = (firstLine.split(delimiter).length - 1);
  
  return firstLineDelimiters > 0 && lines.every(line => {
    const delimiters = (line.split(delimiter).length - 1);
    return delimiters === firstLineDelimiters;
  });
};

const insertTableIntoView = (view: EditorView, tableData: string[][]): void => {
  const { state, dispatch } = view;
  const { tr } = state;

  if (tableData.length === 0 || tableData[0].length === 0) return;

  const cols = Math.max(...tableData.map(row => row.length));

  try {
    // Check if table nodes exist in schema
    if (!state.schema.nodes.table || !state.schema.nodes.tableRow || 
        !state.schema.nodes.tableHeader || !state.schema.nodes.tableCell) {
      console.error("Table nodes not found in schema");
      return;
    }

    // Insert table structure
    const tableNode = state.schema.nodes.table.create({}, 
      tableData.map((rowData, rowIndex) => 
        state.schema.nodes.tableRow.create({}, 
          Array.from({ length: cols }, (_, colIndex) => {
            const cellContent = rowData[colIndex] || "";
            const cellNode = rowIndex === 0 ? 
              state.schema.nodes.tableHeader : 
              state.schema.nodes.tableCell;
            
            return cellNode.create({}, 
              cellContent ? [state.schema.text(cellContent)] : []
            );
          })
        )
      )
    );

    const pos = state.selection.anchor;
    tr.replaceWith(pos, pos, tableNode);
    dispatch(tr);
  } catch (error) {
    console.error("Failed to insert table:", error);
  }
};

const handleHTMLTablePaste = (view: EditorView, html: string): boolean => {
  try {
    // Parse HTML table
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");
    const table = doc.querySelector("table");
    
    if (!table) return false;

    const rows = Array.from(table.querySelectorAll("tr"));
    const tableData = rows.map(row => 
      Array.from(row.querySelectorAll("td, th")).map(cell => 
        cell.textContent?.trim() || ""
      )
    );

    insertTableIntoView(view, tableData);
    return true;
  } catch (error) {
    console.error("Failed to parse HTML table:", error);
    return false;
  }
};

const handleTabularTextPaste = (view: EditorView, text: string): boolean => {
  const lines = text.trim().split('\n');
  const delimiter = text.includes('\t') ? '\t' : ',';
  
  const tableData = lines.map(line => 
    line.split(delimiter).map(cell => cell.trim())
  );

  insertTableIntoView(view, tableData);
  return true;
};

const handleCSVFile = (view: EditorView, file: File): void => {
  const reader = new FileReader();
  reader.onload = (e: ProgressEvent<FileReader>) => {
    const result = e.target?.result;
    if (typeof result === 'string' && isTabularData(result)) {
      handleTabularTextPaste(view, result);
    }
  };
  reader.readAsText(file);
};

const insertTableFromText = (editor: Editor, text: string): void => {
  const lines = text.trim().split('\n');
  const delimiter = text.includes('\t') ? '\t' : ',';
  
  const tableData = lines.map(line => 
    line.split(delimiter).map(cell => cell.trim())
  );

  if (tableData.length === 0 || tableData[0].length === 0) return;

  const rows = tableData.length;
  const cols = Math.max(...tableData.map(row => row.length));

  try {
    // Insert empty table first
    editor.chain().focus().insertTable({ 
      rows, 
      cols, 
      withHeaderRow: true 
    }).run();

    // Fill the table with data
    setTimeout(() => {
      tableData.forEach((rowData, rowIndex) => {
        rowData.forEach((cellData, colIndex) => {
          if (cellData && editor.isActive("table")) {
            try {
              // Navigate to the correct cell position
              if (rowIndex === 0 && colIndex === 0) {
                // First cell, just focus
                editor.commands.focus();
              } else {
                // Move to next cell
                editor.commands.goToNextCell();
              }
              
              // Clear any existing content and insert new content
              editor.commands.selectAll();
              editor.commands.deleteSelection();
              editor.commands.insertContent(cellData);
            } catch (cellError) {
              console.warn(`Failed to fill cell at row ${rowIndex}, col ${colIndex}:`, cellError);
            }
          }
        });
      });
    }, 100);
  } catch (error) {
    console.error("Failed to insert table from text:", error);
  }
};

// Enhanced table copy-paste extension
const TableCopyPaste = Extension.create({
  name: "tableCopyPaste",

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey("tableCopyPaste"),
        props: {
          handlePaste: (view: EditorView, event: ClipboardEvent) => {
            const clipboardData = event.clipboardData;
            if (!clipboardData) return false;

            // Check if we're pasting plain text that looks like a table
            const text = clipboardData.getData("text/plain");
            const html = clipboardData.getData("text/html");

            // Handle Excel/Google Sheets HTML table paste
            if (html && html.includes("<table")) {
              return handleHTMLTablePaste(view, html);
            }

            // Handle tab-separated or comma-separated values
            if (text && isTabularData(text)) {
              return handleTabularTextPaste(view, text);
            }

            return false;
          },

          handleDrop: (view: EditorView, event: DragEvent) => {
            // Handle drag and drop of tabular data
            const files = Array.from(event.dataTransfer?.files || []);
            const csvFile = files.find(file => 
              file.type === "text/csv" || file.name.endsWith(".csv")
            );

            if (csvFile) {
              handleCSVFile(view, csvFile);
              return true;
            }

            return false;
          }
        }
      })
    ];
  },

  addCommands() {
    return {
      // Command to copy table as HTML
      copyTableAsHTML: () => ({ editor }) => {
        if (!editor.isActive("table")) return false;

        try {
          // Simple implementation - copy selected content as HTML
          const html = editor.view.dom.innerHTML;
          navigator.clipboard.writeText(html);
          return true;
        } catch (error) {
          console.error("Failed to copy table:", error);
          return false;
        }
      },

      // Command to paste table from clipboard
      pasteTableFromClipboard: () => ({ editor }) => {
        navigator.clipboard.readText().then(text => {
          if (isTabularData(text)) {
            insertTableFromText(editor, text);
          }
        }).catch(error => {
          console.error("Failed to read clipboard:", error);
        });
        return true;
      }
    };
  }
});

// Enhanced keyboard shortcuts with proper table support
const EnhancedKeyboardShortcuts = Extension.create({
  name: "enhancedKeyboardShortcuts",

  addKeyboardShortcuts() {
    return {
      // Basic formatting shortcuts
      "Mod-Alt-1": () =>
        this.editor.chain().focus().toggleHeading({ level: 1 }).run(),
      "Mod-Alt-2": () =>
        this.editor.chain().focus().toggleHeading({ level: 2 }).run(),
      "Mod-Alt-3": () =>
        this.editor.chain().focus().toggleHeading({ level: 3 }).run(),
      "Mod-Alt-0": () => this.editor.chain().focus().setParagraph().run(),
      "Mod-Shift-8": () => this.editor.chain().focus().toggleBulletList().run(),
      "Mod-Shift-7": () =>
        this.editor.chain().focus().toggleOrderedList().run(),
      "Mod-Shift-.": () => this.editor.chain().focus().toggleBlockquote().run(),
      "Mod-Shift-x": () => this.editor.chain().focus().toggleTaskList().run(),

      // Text alignment shortcuts
      "Mod-Shift-l": () => this.editor.chain().focus().setTextAlign("left").run(),
      "Mod-Shift-e": () => this.editor.chain().focus().setTextAlign("center").run(),
      "Mod-Shift-r": () => this.editor.chain().focus().setTextAlign("right").run(),
      "Mod-Shift-j": () => this.editor.chain().focus().setTextAlign("justify").run(),

      // Table shortcuts
      "Mod-Alt-t": () => {
        if (this.editor.can().insertTable()) {
          return this.editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
        }
        return false;
      },
      
      "Tab": () => {
        if (this.editor.isActive("table")) {
          return this.editor.chain().focus().goToNextCell().run();
        }
        return false;
      },
      "Shift-Tab": () => {
        if (this.editor.isActive("table")) {
          return this.editor.chain().focus().goToPreviousCell().run();
        }
        return false;
      },

      // Add row above/below
      "Mod-Alt-ArrowUp": () => {
        if (this.editor.isActive("table") && this.editor.can().addRowBefore()) {
          return this.editor.chain().focus().addRowBefore().run();
        }
        return false;
      },
      "Mod-Alt-ArrowDown": () => {
        if (this.editor.isActive("table") && this.editor.can().addRowAfter()) {
          return this.editor.chain().focus().addRowAfter().run();
        }
        return false;
      },

      // Add column left/right  
      "Mod-Alt-ArrowLeft": () => {
        if (this.editor.isActive("table") && this.editor.can().addColumnBefore()) {
          return this.editor.chain().focus().addColumnBefore().run();
        }
        return false;
      },
      "Mod-Alt-ArrowRight": () => {
        if (this.editor.isActive("table") && this.editor.can().addColumnAfter()) {
          return this.editor.chain().focus().addColumnAfter().run();
        }
        return false;
      },

      // Delete row/column
      "Mod-Shift-Backspace": () => {
        if (this.editor.isActive("table") && this.editor.can().deleteRow()) {
          return this.editor.chain().focus().deleteRow().run();
        }
        return false;
      },
      "Mod-Alt-Backspace": () => {
        if (this.editor.isActive("table") && this.editor.can().deleteColumn()) {
          return this.editor.chain().focus().deleteColumn().run();
        }
        return false;
      },

      // Delete entire table
      "Mod-Shift-Alt-Backspace": () => {
        if (this.editor.isActive("table") && this.editor.can().deleteTable()) {
          return this.editor.chain().focus().deleteTable().run();
        }
        return false;
      },
    };
  },
});

// Usage example - add to your extensions array:
// const enhancedExtensions = [
//   // ...your existing extensions,
//   TableCopyPaste,
//   EnhancedKeyboardShortcuts,
// ];

export { TableCopyPaste, EnhancedKeyboardShortcuts };