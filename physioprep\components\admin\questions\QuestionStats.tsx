"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  FileQuestion, 
  CheckCircle, 
  Crown, 
  TrendingUp,
  Target,
  BarChart3
} from "lucide-react";
import { QuestionAdminStats } from "@/actions/questions";

interface QuestionStatsProps {
  stats: QuestionAdminStats;
}

export function QuestionStats({ stats }: QuestionStatsProps) {
  const {
    totalQuestions,
    activeQuestions,
    inactiveQuestions,
    freeQuestions,
    premiumQuestions,
    easyQuestions,
    mediumQuestions,
    hardQuestions,
    avgUsageCount,
    avgCorrectAnswerCount,
  } = stats;

  // Calculate percentages
  const activePercentage = totalQuestions > 0 ? (activeQuestions / totalQuestions) * 100 : 0;
  const premiumPercentage = totalQuestions > 0 ? (premiumQuestions / totalQuestions) * 100 : 0;
  const averageAccuracy = avgUsageCount > 0 ? (avgCorrectAnswerCount / avgUsageCount) * 100 : 0;

  // Difficulty distribution
  const difficultyData = [
    { label: "Easy", count: easyQuestions, color: "bg-green-500" },
    { label: "Medium", count: mediumQuestions, color: "bg-yellow-500" },
    { label: "Hard", count: hardQuestions, color: "bg-red-500" },
  ];

  const maxDifficultyCount = Math.max(easyQuestions, mediumQuestions, hardQuestions);

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Questions */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Questions</CardTitle>
          <FileQuestion className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalQuestions.toLocaleString()}</div>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-1">
            <Badge variant="outline" className="text-xs">
              {activeQuestions} active
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {inactiveQuestions} inactive
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Active Status */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Active Questions</CardTitle>
          <CheckCircle className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{activeQuestions.toLocaleString()}</div>
          <div className="flex items-center space-x-2 mt-2">
            <Progress value={activePercentage} className="flex-1" />
            <span className="text-xs text-muted-foreground">
              {activePercentage.toFixed(1)}%
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Tier Distribution */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Premium Content</CardTitle>
          <Crown className="h-4 w-4 text-yellow-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{premiumQuestions.toLocaleString()}</div>
          <div className="flex items-center space-x-2 mt-2">
            <Progress value={premiumPercentage} className="flex-1" />
            <span className="text-xs text-muted-foreground">
              {premiumPercentage.toFixed(1)}%
            </span>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {freeQuestions} free questions
          </p>
        </CardContent>
      </Card>

      {/* Average Accuracy */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Average Accuracy</CardTitle>
          <Target className="h-4 w-4 text-blue-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{averageAccuracy.toFixed(1)}%</div>
          <div className="flex items-center space-x-2 mt-2">
            <Progress value={averageAccuracy} className="flex-1" />
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {avgUsageCount.toFixed(1)} avg attempts
          </p>
        </CardContent>
      </Card>

      {/* Difficulty Distribution */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-sm font-medium flex items-center">
            <BarChart3 className="mr-2 h-4 w-4" />
            Difficulty Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {difficultyData.map((item) => (
              <div key={item.label} className="flex items-center space-x-3">
                <div className="w-16 text-sm font-medium">{item.label}</div>
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${item.color}`}
                    style={{
                      width: maxDifficultyCount > 0 ? `${(item.count / maxDifficultyCount) * 100}%` : '0%'
                    }}
                  />
                </div>
                <div className="w-12 text-sm text-right">{item.count}</div>
                <div className="w-12 text-xs text-muted-foreground text-right">
                  {totalQuestions > 0 ? ((item.count / totalQuestions) * 100).toFixed(1) : 0}%
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Usage Statistics */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-sm font-medium flex items-center">
            <TrendingUp className="mr-2 h-4 w-4" />
            Usage Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Average Usage</span>
                <span className="text-sm font-medium">{avgUsageCount.toFixed(1)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Correct Answers</span>
                <span className="text-sm font-medium">{avgCorrectAnswerCount.toFixed(1)}</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Success Rate</span>
                <Badge variant={averageAccuracy >= 70 ? "default" : averageAccuracy >= 50 ? "secondary" : "destructive"}>
                  {averageAccuracy.toFixed(1)}%
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Total Attempts</span>
                <span className="text-sm font-medium">
                  {(avgUsageCount * totalQuestions).toFixed(0)}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
