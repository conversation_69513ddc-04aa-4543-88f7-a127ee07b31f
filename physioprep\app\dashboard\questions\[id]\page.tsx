import { notFound } from "next/navigation";
import Link from "next/link";
import { ArrowLeft, Edit } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { getQuestionById } from "@/actions/questions";
import { Question } from "@/types/types";

interface QuestionDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function QuestionDetailPage({ params }: QuestionDetailPageProps) {
  const { id } = await params;

  try {
    const result = await getQuestionById(id);
    
    if (!result.success) {
      notFound();
    }

    const question = result.data as Question;
    const topic = typeof question.topic === 'object' ? question.topic : null;
    const subject = typeof question.subject === 'object' ? question.subject : null;

    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/questions">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Questions
            </Link>
          </Button>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Question Details</h2>
            <p className="text-muted-foreground">
              View and manage question information.
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" asChild>
              <Link href={`/dashboard/questions/${question._id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Link>
            </Button>
          </div>
        </div>

        <div className="grid gap-6">
          {/* Question Information */}
          <Card>
            <CardHeader>
              <CardTitle>Question Information</CardTitle>
              <CardDescription>
                Basic information about this question.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-2">Question Text</h4>
                <p className="text-base">{question.text}</p>
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-2">Subject</h4>
                  <p className="text-base">{subject?.name || 'Unknown'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-2">Topic</h4>
                  <p className="text-base">{topic?.topicName || 'Unknown'}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-2">Difficulty</h4>
                  <Badge variant={
                    question.difficulty === "easy" ? "default" : 
                    question.difficulty === "medium" ? "secondary" : 
                    "destructive"
                  }>
                    {question.difficulty.charAt(0).toUpperCase() + question.difficulty.slice(1)}
                  </Badge>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-2">Tier</h4>
                  <Badge variant={question.tier === "premium" ? "destructive" : "outline"}>
                    {question.tier.charAt(0).toUpperCase() + question.tier.slice(1)}
                  </Badge>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-2">Status</h4>
                  <Badge variant={question.isActive ? "default" : "secondary"}>
                    {question.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Answer Options */}
          <Card>
            <CardHeader>
              <CardTitle>Answer Options</CardTitle>
              <CardDescription>
                Available answer choices for this question.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {question.options.map((option, index) => (
                  <div 
                    key={index} 
                    className={`p-4 rounded-lg border shadow-2xl ${
                      option.isCorrect 
                        ? ' border-green-400 ' 
                        : 'bg-muted'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-base">{option.text}</span>
                      {option.isCorrect && (
                        <Badge variant="default" className="bg-green-600 p-2">
                          Correct
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Explanation */}
          {question.explanationHtml && (
            <Card>
              <CardHeader>
                <CardTitle>Explanation</CardTitle>
                <CardDescription>
                  Detailed explanation for the correct answer.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div 
                  className="prose prose-sm max-w-none"
                  dangerouslySetInnerHTML={{ __html: question.explanationHtml }}
                />
              </CardContent>
            </Card>
          )}

          {/* Statistics */}
          {(question.usageCount || question.correctAnswerCount) && (
            <Card>
              <CardHeader>
                <CardTitle>Usage Statistics</CardTitle>
                <CardDescription>
                  Performance metrics for this question.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">Total Attempts</h4>
                    <p className="text-2xl font-bold">{question.usageCount || 0}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">Correct Answers</h4>
                    <p className="text-2xl font-bold">{question.correctAnswerCount || 0}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">Accuracy Rate</h4>
                    <p className="text-2xl font-bold">
                      {question.usageCount && question.usageCount > 0 
                        ? Math.round((question.correctAnswerCount || 0) / question.usageCount * 100)
                        : 0
                      }%
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error loading question:", error);
    notFound();
  }
}
