import { notFound } from "next/navigation";
import { TopicForm } from "../../components/TopicForm";
import { getTopicById } from "../../actions";

interface EditTopicPageProps {
  params: Promise<{ id: string }>;
}

export default async function EditTopicPage({ params }: EditTopicPageProps) {
  const { id } = await params;
  const result = await getTopicById(id);

  if (!result.success) {
    notFound();
  }

  return <TopicForm topic={result.data} mode="edit" />;
}
