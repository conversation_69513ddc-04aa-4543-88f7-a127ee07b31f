const { check, validationResult } = require('express-validator');

// Validation middleware to check for errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  next();
};

// Create quiz validation
exports.validateCreateQuiz = [
  check('title', 'Title is required').not().isEmpty(),
  check('title', 'Title must be between 3 and 100 characters').isLength({ min: 3, max: 100 }),
  check('description', 'Description is required').not().isEmpty(),
  check('description', 'Description must be between 10 and 500 characters').isLength({ min: 10, max: 500 }),
  check('subject', 'Subject is required').isMongoId(),
  check('topic', 'Topic is required').isMongoId(),
  check('questions', 'Questions are required').isArray({ min: 1 }),
  check('questions.*.text', 'Question text is required').not().isEmpty(),
  check('questions.*.options', 'Options are required').isArray({ min: 2, max: 6 }),
  check('questions.*.options.*.text', 'Option text is required').not().isEmpty(),
  check('questions.*.options.*.isCorrect', 'Option correctness is required').isBoolean(),
  check('timeLimit', 'Time limit is required').isInt({ min: 1, max: 3600 }),
  check('difficulty', 'Difficulty is required').isIn(['easy', 'medium', 'hard']),
  check('tier', 'Tier is required').isIn(['free', 'premium']),
  handleValidationErrors
];

// Update quiz validation
exports.validateUpdateQuiz = [
  check('title', 'Title must be between 3 and 100 characters').optional().isLength({ min: 3, max: 100 }),
  check('description', 'Description must be between 10 and 500 characters').optional().isLength({ min: 10, max: 500 }),
  check('subject', 'Subject must be a valid MongoDB ID').optional().isMongoId(),
  check('topic', 'Topic must be a valid MongoDB ID').optional().isMongoId(),
  check('questions', 'Questions must be an array').optional().isArray({ min: 1 }),
  check('questions.*.text', 'Question text is required').optional().not().isEmpty(),
  check('questions.*.options', 'Options must be an array with 2-6 items').optional().isArray({ min: 2, max: 6 }),
  check('questions.*.options.*.text', 'Option text is required').optional().not().isEmpty(),
  check('questions.*.options.*.isCorrect', 'Option correctness must be a boolean').optional().isBoolean(),
  check('timeLimit', 'Time limit must be between 1 and 3600 seconds').optional().isInt({ min: 1, max: 3600 }),
  check('difficulty', 'Difficulty must be easy, medium, or hard').optional().isIn(['easy', 'medium', 'hard']),
  check('tier', 'Tier must be free or premium').optional().isIn(['free', 'premium']),
  handleValidationErrors
];

// Submit quiz validation
exports.validateSubmitQuiz = [
  check('answers', 'Answers are required').isArray(),
  check('answers.*.selectedAnswer', 'Each answer must be a valid option index').isInt({ min: 0 }),
  check('timeSpent', 'Time spent is required').isInt({ min: 0 }),
  handleValidationErrors
]; 