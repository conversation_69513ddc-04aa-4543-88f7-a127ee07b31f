const { body, param, query, validationResult, check } = require('express-validator');
const mongoose = require('mongoose');

// Validation middleware to check for errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  next();
};

// Custom validator for MongoDB ObjectId
const isValidObjectId = (value) => {
  return mongoose.Types.ObjectId.isValid(value);
};

// Create question validation
const validateCreateQuestion = [
  check('text', 'Question text is required').not().isEmpty(),
  check('text', 'Question text must be between 10 and 1000 characters').isLength({ min: 10, max: 1000 }),
  check('options', 'Options are required').isArray({ min: 2, max: 6 }),
  check('options.*.text', 'Option text is required').not().isEmpty(),
  check('options.*.text', 'Option text must be between 1 and 200 characters').isLength({ min: 1, max: 200 }),
  check('options.*.isCorrect', 'Option correctness is required').isBoolean(),
  check('explanationJson')
    .optional()
    .custom((value) => {
      if (value && typeof value === 'string') {
        try {
          JSON.parse(value);
        } catch (e) {
          throw new Error('Explanation JSON must be valid JSON');
        }
      }
      return true;
    }),
  check('explanationHtml')
    .optional()
    .trim(),
  check('topic', 'Topic is required').isMongoId(),
  check('difficulty', 'Difficulty is required').isIn(['easy', 'medium', 'hard']),
  check('tier', 'Tier is required').isIn(['free', 'premium']),
  check('isActive', 'isActive must be a boolean').optional().isBoolean(),
  handleValidationErrors
];

// Update question validation
const validateUpdateQuestion = [
  check('text', 'Question text must be between 10 and 1000 characters').optional().isLength({ min: 10, max: 1000 }),
  check('options', 'Options must be an array with 2-6 items').optional().isArray({ min: 2, max: 6 }),
  check('options.*.text', 'Option text must be between 1 and 200 characters').optional().isLength({ min: 1, max: 200 }),
  check('options.*.isCorrect', 'Option correctness must be a boolean').optional().isBoolean(),
  check('explanationJson')
    .optional()
    .custom((value) => {
      if (value && typeof value === 'string') {
        try {
          JSON.parse(value);
        } catch (e) {
          throw new Error('Explanation JSON must be valid JSON');
        }
      }
      return true;
    }),
  check('explanationHtml')
    .optional()
    .trim(),
  check('topic', 'Topic must be a valid MongoDB ID').optional().isMongoId(),
  check('difficulty', 'Difficulty must be easy, medium, or hard').optional().isIn(['easy', 'medium', 'hard']),
  check('tier', 'Tier must be free or premium').optional().isIn(['free', 'premium']),
  check('isActive', 'isActive must be a boolean').optional().isBoolean(),
  handleValidationErrors
];

// Get questions validation
const validateGetQuestions = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  query('subject')
    .optional()
    .custom(isValidObjectId)
    .withMessage('Subject must be a valid ObjectId'),
  
  query('category')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Category must be between 1 and 100 characters'),
  
  query('difficulty')
    .optional()
    .isIn(['easy', 'medium', 'hard'])
    .withMessage('Difficulty must be one of: easy, medium, hard'),
  
  query('tier')
    .optional()
    .isIn(['free', 'premium'])
    .withMessage('Tier must be one of: free, premium'),
  
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
  
  query('sortBy')
    .optional()
    .isIn(['createdAt', 'difficulty', 'accuracy', 'timesAnswered'])
    .withMessage('SortBy must be one of: createdAt, difficulty, accuracy, timesAnswered'),
  
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('SortOrder must be one of: asc, desc'),
  
  handleValidationErrors
];

// Question ID parameter validation
const validateQuestionId = [
  param('id')
    .custom(isValidObjectId)
    .withMessage('Question ID must be a valid ObjectId'),
  
  handleValidationErrors
];

// Submit answer validation
const validateSubmitAnswer = [
  param('id')
    .custom(isValidObjectId)
    .withMessage('Question ID must be a valid ObjectId'),
  
  body('selectedOptionId')
    .custom(isValidObjectId)
    .withMessage('Selected option ID must be a valid ObjectId'),
  
  body('timeSpent')
    .optional()
    .isInt({ min: 0, max: 3600 })
    .withMessage('Time spent must be between 0 and 3600 seconds'),
  
  handleValidationErrors
];

// Bulk operations validation
const validateBulkDelete = [
  body('questionIds')
    .isArray({ min: 1, max: 100 })
    .withMessage('Question IDs must be an array with 1-100 items'),
  
  body('questionIds.*')
    .custom(isValidObjectId)
    .withMessage('Each question ID must be a valid ObjectId'),
  
  handleValidationErrors
];

const validateBulkUpdate = [
  body('questionIds')
    .isArray({ min: 1, max: 100 })
    .withMessage('Question IDs must be an array with 1-100 items'),
  
  body('questionIds.*')
    .custom(isValidObjectId)
    .withMessage('Each question ID must be a valid ObjectId'),
  
  body('updates')
    .isObject()
    .withMessage('Updates must be an object'),
  
  body('updates.difficulty')
    .optional()
    .isIn(['easy', 'medium', 'hard'])
    .withMessage('Difficulty must be one of: easy, medium, hard'),
  
  body('updates.tier')
    .optional()
    .isIn(['free', 'premium'])
    .withMessage('Tier must be one of: free, premium'),
  
  body('updates.isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  
  handleValidationErrors
];

module.exports = {
  validateCreateQuestion,
  validateUpdateQuestion,
  validateGetQuestions,
  validateQuestionId,
  validateSubmitAnswer,
  validateBulkDelete,
  validateBulkUpdate,
  handleValidationErrors
};
