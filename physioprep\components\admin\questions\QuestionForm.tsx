"use client";

import { useActionState, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";

import {
  createQuestion,
  updateQuestion,
  QuestionFormState,
  getSubjectsForDropdown,
  getTopicsBySubject,
} from "@/actions/questions";
import { Question, Topic } from "@/types/types";
import { <PERSON><PERSON><PERSON><PERSON>, Save, Plus, Trash2, FileText } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { generateHTML } from "@tiptap/html";
import { BulkOptionsPaste } from "./BulkOptionsPaste";

import NovelEditor from "@/components/editor/NovelEditor";
import { JSONContent } from "novel";
import { defaultExtensions } from "@/components/editor/extentions";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { getQuestionByIdAtom, questionsAtom } from "@/store/questions";
import { SubjectsAtom } from "@/store/Subject";

interface QuestionFormProps {
  id?: string;
  mode: "create" | "edit";
}

interface QuestionOption {
  text: string;
  isCorrect: boolean;
}

export function QuestionForm({ id, mode }: QuestionFormProps) {
  const router = useRouter();
  const isEdit = mode === "edit";
  const [subjects, setSubjects] = useAtom(SubjectsAtom);
  const [topics, setTopics] = useState<Topic[]>([]);
  const [question, setQuestion] = useState<Question | null>(null);
  const questions = useAtomValue(questionsAtom);
  const getQuestionById = useSetAtom(getQuestionByIdAtom);

  const [selectedSubject, setSelectedSubject] = useState("");
  const [selectedTopic, setSelectedTopic] = useState("");
  const [explanationJson, setExplanationJson] = useState<JSONContent | null>(
    null
  );


  const [options, setOptions] = useState<QuestionOption[]>([
    { text: "", isCorrect: false },
    { text: "", isCorrect: false },
    { text: "", isCorrect: false },
    { text: "", isCorrect: false },
  ]);

  const [loadingSubjects, setLoadingSubjects] = useState(false);
  const [loadingTopics, setLoadingTopics] = useState(false);
  const [isActive, setIsActive] = useState(true);

  const action =
    isEdit && question && id
      ? updateQuestion.bind(null, question._id)
      : createQuestion;

  const [state, formAction, isPending] = useActionState<
    QuestionFormState,
    FormData
  >(action, { error: null, success: false });

  /* ------------------------- useEffect for questions ------------------------ */

  useEffect(() => {
    if (id && mode === "edit") {
      // First try to find the question in the atom store
      const existingQuestion = questions.find((q) => q._id === id);

      if (existingQuestion) {
        setQuestion(existingQuestion);
        setIsActive(existingQuestion?.isActive ?? true);

        // Parse and set explanation JSON
        if (existingQuestion.explanationJson) {
          console.log(
            "existingQuestion.explanationJson:",
            typeof existingQuestion.explanationJson
          );
          try {
            const parsedJson =
              typeof existingQuestion.explanationJson === "string"
                ? JSON.parse(existingQuestion.explanationJson)
                : existingQuestion.explanationJson;
            setExplanationJson(parsedJson);
            console.log("parsedJson:", parsedJson);
          } catch (error) {
            console.error("Failed to parse question explanationJson:", error);
            // Fallback to plain text if JSON parsing fails
            setExplanationJson({
              type: "doc",
              content: [
                {
                  type: "paragraph",
                  content: [
                    {
                      type: "text",
                      text:
                        typeof existingQuestion.explanationJson === "string"
                          ? existingQuestion.explanationJson
                          : "",
                    },
                  ],
                },
              ],
            });
          }
        }

        // Set options
        setOptions(
          existingQuestion?.options && existingQuestion.options.length > 0
            ? existingQuestion.options
            : [
                { text: "", isCorrect: false },
                { text: "", isCorrect: false },
                { text: "", isCorrect: false },
                { text: "", isCorrect: false },
              ]
        );

        // Set selected subject and topic for edit mode
        if (existingQuestion?.subject) {
          const subjectId =
            typeof existingQuestion.subject === "object"
              ? existingQuestion.subject._id
              : existingQuestion.subject;
          setSelectedSubject(subjectId);
        }

        if (existingQuestion?.topic) {
          const topicId =
            typeof existingQuestion.topic === "object"
              ? existingQuestion.topic._id
              : existingQuestion.topic;
          setSelectedTopic(topicId);
        }
      } else {
        // If not found in store, try to get it via the atom action
        const fetchedQuestion = getQuestionById(id);
        if (fetchedQuestion) {
          setQuestion(fetchedQuestion);
          setIsActive(fetchedQuestion?.isActive ?? true);

          // Apply the same logic for the fetched question
          if (fetchedQuestion.explanationJson) {
            try {
              const parsedJson =
                typeof fetchedQuestion.explanationJson === "string"
                  ? JSON.parse(fetchedQuestion.explanationJson)
                  : fetchedQuestion.explanationJson;
              setExplanationJson(parsedJson);
            } catch (error) {
              console.error("Failed to parse question explanationJson:", error);
              setExplanationJson({
                type: "doc",
                content: [
                  {
                    type: "paragraph",
                    content: [
                      {
                        type: "text",
                        text:
                          typeof fetchedQuestion.explanationJson === "string"
                            ? fetchedQuestion.explanationJson
                            : "",
                      },
                    ],
                  },
                ],
              });
            }
          }

          setOptions(
            fetchedQuestion?.options && fetchedQuestion.options.length > 0
              ? fetchedQuestion.options
              : [
                  { text: "", isCorrect: false },
                  { text: "", isCorrect: false },
                  { text: "", isCorrect: false },
                  { text: "", isCorrect: false },
                ]
          );

          if (fetchedQuestion?.subject) {
            const subjectId =
              typeof fetchedQuestion.subject === "object"
                ? fetchedQuestion.subject._id
                : fetchedQuestion.subject;
            setSelectedSubject(subjectId);
          }

          if (fetchedQuestion?.topic) {
            const topicId =
              typeof fetchedQuestion.topic === "object"
                ? fetchedQuestion.topic._id
                : fetchedQuestion.topic;
            setSelectedTopic(topicId);
          }
        }
      }
    } else
      setExplanationJson({
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: "Write From Here",
              },
            ],
          },
        ],
      });
  }, [id, mode, questions, getQuestionById]);

  /* ----------------------- Fetch subjects for dropdown ---------------------- */

  async function fetchSubjects() {
    setLoadingSubjects(true);
    try {
      const result = await getSubjectsForDropdown();
      if (result.success) {
        setSubjects(result.data);
      } else {
        toast.error(result.errors[0]?.msg);
      }
    } catch {
      toast.error("Failed to load subjects");
    } finally {
      setLoadingSubjects(false);
    }
  }

  useEffect(() => {
    if (subjects.length === 0) {
      fetchSubjects();
    }
  }, []);

  // Fetch topics when subject changes
  useEffect(() => {
    async function fetchTopics() {
      if (!selectedSubject) {
        setTopics([]);
        return;
      }

      setLoadingTopics(true);
      try {
        const result = await getTopicsBySubject(selectedSubject);
        if (result.success) {
          setTopics(result.data);
        } else {
          toast.error(result.errors[0]?.msg);
          setTopics([]);
        }
      } catch {
        toast.error("Failed to load topics");
        setTopics([]);
      } finally {
        setLoadingTopics(false);
      }
    }

    fetchTopics();
  }, [selectedSubject]);

  // Handle form success and errors
  useEffect(() => {
    if (state?.success) {
      toast.success(
        isEdit
          ? "Question updated successfully!"
          : "Question created successfully!"
      );

      // Navigate back to questions list
      router.push("/dashboard/questions");
    } else if (state?.error) {
      toast.error(state.error);
    }
  }, [state?.success, state?.error, state?.data, isEdit, router]);

  // Handle subject change
  const handleSubjectChange = (subjectId: string) => {
    setSelectedSubject(subjectId);
    setSelectedTopic(""); // Reset topic when subject changes
  };

  // Handle option changes
  const updateOption = (
    index: number,
    field: keyof QuestionOption,
    value: string | boolean
  ) => {
    setOptions((prev) =>
      prev.map((option, i) =>
        i === index ? { ...option, [field]: value } : option
      )
    );
  };

  // Add new option
  const addOption = () => {
    if (options.length < 6) {
      setOptions((prev) => [...prev, { text: "", isCorrect: false }]);
    }
  };

  // Remove option
  const removeOption = (index: number) => {
    if (options.length > 2) {
      setOptions((prev) => prev.filter((_, i) => i !== index));
    }
  };

  // Handle bulk options update
  const handleBulkOptionsUpdate = (newOptions: QuestionOption[]) => {
    setOptions(newOptions);
  };

  const handleSubmit = (formData: FormData) => {
    try {
      // Validate question text
      if (!hasQuestionText(formData)) {
        toast.error("Question text is required");
        return;
      }

      // Validate options
      if (!hasValidOptions) {
        toast.error("At least 2 options with text are required");
        return;
      }

      if (!hasCorrectAnswer) {
        toast.error("At least one correct answer is required");
        return;
      }

      if (!hasOnlyOneCorrectAnswer) {
        toast.error("Only one correct answer is allowed");
        return;
      }

      if (!selectedTopic) {
        toast.error("Please select a topic");
        return;
      }

      const explanationJsonString = JSON.stringify(explanationJson);
      const explanationHtml = getHtmlPreview(explanationJson as JSONContent);

      formData.set("explanationJson", explanationJsonString);
      formData.set("explanationHtml", explanationHtml);

      // Add options to form data
      options.forEach((option, index) => {
        formData.set(`option_${index}_text`, option.text);
        formData.set(
          `option_${index}_isCorrect`,
          option.isCorrect ? "on" : "off"
        );
      });

      // Add switch states to form data
      formData.set("isActive", isActive ? "on" : "off");
      formData.set("topic", selectedTopic);

      formAction(formData);
    } catch (error) {
      console.error("Error preparing form data:", error);
      toast.error("Failed to prepare form data. Please try again.");
    }
  };

  // Helper function to convert rich content to HTML
  const getHtmlPreview = (content: JSONContent): string => {
    try {
      if (!content || !content.content) return "";
      const html = generateHTML(content, defaultExtensions);
      return html;
    } catch (error) {
      console.error("Error generating HTML from JSONContent:", error);
      return "";
    }
  };

  // Validation
  const hasCorrectAnswer = options.some((option) => option.isCorrect);
  const hasValidOptions =
    options.filter((option) => option.text.trim()).length >= 2;
  const hasOnlyOneCorrectAnswer =
    options.filter((option) => option.isCorrect).length === 1;
  const hasQuestionText = (formData: FormData) => {
    const text = formData.get("text") as string;
    return text && text.trim().length > 0;
  };
  const canSubmit =
    hasCorrectAnswer &&
    hasValidOptions &&
    selectedTopic &&
    hasOnlyOneCorrectAnswer;

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/questions">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Questions
          </Link>
        </Button>
      </div>

      <div>
        <h2 className="text-3xl font-bold tracking-tight">
          {isEdit ? "Edit Question" : "Create Question"}
        </h2>
        <p className="text-muted-foreground">
          {isEdit
            ? "Update the question information below."
            : "Add a new question to the system."}
        </p>
      </div>

      {state?.error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {state.error}
        </div>
      )}

      <Card className="max-w-6xl shadow-sm">
        <CardHeader className="pb-6">
          <div className="flex items-center gap-3">
            <FileText className="h-6 w-6 text-primary" />
            <div>
              <CardTitle>Question Details</CardTitle>
              <CardDescription>
                Enter the question information and configure its settings.
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-8">
          <form action={handleSubmit} className="space-y-8">
            {/* Question Text */}
            <div className="space-y-2">
              <Label htmlFor="text" className="text-base font-medium">
                Question Text *
              </Label>
              <Textarea
                id="text"
                name="text"
                placeholder="Enter the question text..."
                defaultValue={question?.text || ""}
                required
                className="min-h-[100px]"
              />
            </div>

            {/* Subject and Topic Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="subject" className="text-base font-medium">
                  Subject *
                </Label>
                <Select
                  value={selectedSubject}
                  onValueChange={handleSubjectChange}
                  disabled={loadingSubjects}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a subject" />
                  </SelectTrigger>
                  <SelectContent>
                    {subjects.map((subject) => (
                      <SelectItem key={subject._id} value={subject._id}>
                        {subject.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="topic" className="text-base font-medium">
                  Topic *
                </Label>
                <Select
                  value={selectedTopic}
                  onValueChange={setSelectedTopic}
                  disabled={!selectedSubject || loadingTopics}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        selectedSubject
                          ? "Select a topic"
                          : "Select subject first"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {topics.map((topic) => (
                      <SelectItem key={topic._id} value={topic._id}>
                        {topic.topicName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Options */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">
                  Answer Options * (At least one must be correct)
                </Label>
                <div className="flex items-center gap-2">
                  <BulkOptionsPaste
                    onOptionsUpdate={handleBulkOptionsUpdate}
                    currentOptions={options}
                    maxOptions={6}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addOption}
                    disabled={options.length >= 6}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add Option
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                {options.map((option, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-3 p-3 border rounded-lg"
                  >
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        checked={option.isCorrect}
                        onCheckedChange={(checked) =>
                          updateOption(index, "isCorrect", checked as boolean)
                        }
                      />
                      <Label className="text-sm font-medium">Correct</Label>
                    </div>

                    <Input
                      placeholder={`Option ${index + 1}`}
                      value={option.text}
                      onChange={(e) =>
                        updateOption(index, "text", e.target.value)
                      }
                      className="flex-1"
                    />

                    {options.length > 2 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeOption(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>

              {!hasCorrectAnswer && (
                <p className="text-sm text-red-600">
                  At least one option must be marked as correct.
                </p>
              )}

              {!hasValidOptions && (
                <p className="text-sm text-red-600">
                  At least two options must have text.
                </p>
              )}
            </div>

            {/* Difficulty and Tier */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="difficulty" className="text-base font-medium">
                  Difficulty *
                </Label>
                <Select
                  name="difficulty"
                  defaultValue={question?.difficulty || "medium"}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select difficulty" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="easy">Easy</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="hard">Hard</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tier" className="text-base font-medium">
                  Tier *
                </Label>
                <Select name="tier" defaultValue={question?.tier || "free"}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select tier" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="free">Free</SelectItem>
                    <SelectItem value="premium">Premium</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Explanation */}

              <div className="space-y-4">
                <Label className="text-base font-medium">Explanation</Label>
                <div className="border rounded-lg min-h-[350px]">
                  <NovelEditor
                    data={explanationJson as JSONContent}
                    setData={setExplanationJson}
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  Provide a detailed explanation for the correct answer.
                </p>
              </div>


            <Separator />

            {/* Settings */}
            <div className="space-y-4">
              <Label className="text-base font-medium">Settings</Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={isActive}
                  onCheckedChange={setIsActive}
                />
                <Label htmlFor="isActive">Active</Label>
                <Badge variant={isActive ? "default" : "secondary"}>
                  {isActive ? "Active" : "Inactive"}
                </Badge>
              </div>
            </div>

            <Separator />

            {/* Submit Button */}
            <div className="flex items-center justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/dashboard/questions")}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isPending || !canSubmit}
                className="min-w-[120px]"
              >
                {isPending ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>{isEdit ? "Updating..." : "Creating..."}</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <Save className="h-4 w-4" />
                    <span>
                      {isEdit ? "Update Question" : "Create Question"}
                    </span>
                  </div>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
