"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  EditorRoot,
  handleCommandNavigation,
  handleImageDrop,
  handleImagePaste,
  JSONContent,
} from "novel";
import React from "react";

import { defaultExtensions } from "./extentions";
import { slashCommand, suggestionItems } from "./slash-command";
import {
  EditorCom<PERSON>,
  EditorCommandEmpty,
  EditorCommandItem,
  EditorCommandList,
} from "novel";
import { NodeSelector } from "./selectors/node-selector";
import { LinkSelector } from "./selectors/link-selector";
import { TextButtons } from "./selectors/text-buttons";
import { ColorSelector } from "./selectors/color-selector";
import { AlignmentSelector } from "./selectors/alignment-selector";
import { TableSelector } from "./selectors/table-selector";
import { uploadFn } from "./image-upload";
import { EnhancedKeyboardShortcuts, TableCopyPaste } from "./table-copy-paste";

const extensions = [
  ...defaultExtensions,
  slashCommand,
  EnhancedKeyboardShortcuts, // Replaces the original KeyboardShortcuts
  TableCopyPaste, // New table copy-paste functionality
];

interface NovelEditorProps {
  data: JSONContent;
  setData: (json: JSONContent) => void;
}

const NovelEditor: React.FC<NovelEditorProps> = ({ data, setData }) => {
  const [openNode, setOpenNode] = React.useState(false);
  const [openLink, setOpenLink] = React.useState(false);
  const [openColor, setOpenColor] = React.useState(false);
  const [openAlignment, setOpenAlignment] = React.useState(false);
  const [openTable, setOpenTable] = React.useState(false);

  return (
    <EditorRoot>
      {data &&
      <EditorContent
        initialContent={data}
        onUpdate={({ editor }: { editor: EditorInstance }) => {
          const json = editor.getJSON();
          setData(json);
        }}
        extensions={extensions}
        className="p-4 min-h-[350px] max-w-full prose focus:outline-none [&_.ProseMirror]:min-h-[350px] [&_.ProseMirror]:focus:outline-none"
        editorProps={{
          handleDOMEvents: {
            keydown: (_view, event) => handleCommandNavigation(event),
          },
          handlePaste: (view, event) => {
            // First check if it's an image
            const handled = handleImagePaste(view, event, uploadFn);
            if (handled) return true;
            
            // The TableCopyPaste extension will handle table pasting
            return false;
          },
          handleDrop: (view, event, _slice, moved) => {
            // First check if it's an image
            const imageHandled = handleImageDrop(view, event, moved, uploadFn);
            if (imageHandled) return true;
            
            // The TableCopyPaste extension will handle CSV/table drops
            return false;
          },
          attributes: {
            class:
              "prose prose-lg dark:prose-invert prose-headings:font-title font-default focus:outline-none max-w-full",
          },
        }}
      >
        <EditorBubble
          tippyOptions={{
            placement: "top",
          }}
          className="flex w-fit max-w-[90vw] overflow-hidden rounded border border-muted shadow-xl"
        >
          <NodeSelector open={openNode} onOpenChange={setOpenNode} />
          <LinkSelector open={openLink} onOpenChange={setOpenLink} />
          <TextButtons />
          <AlignmentSelector open={openAlignment} onOpenChange={setOpenAlignment} />
          <TableSelector open={openTable} onOpenChange={setOpenTable} />
          <ColorSelector open={openColor} onOpenChange={setOpenColor} />
        </EditorBubble>
        <EditorCommand className="z-50 h-auto max-h-[330px] bg-background w-72 overflow-y-auto border border-muted rounded-2xl shadow-lg shadow-gray-700 transition-all">
          <EditorCommandEmpty className="px-2 text-muted-foreground">
            No results
          </EditorCommandEmpty>
          <EditorCommandList className="px-2 py-2">
            {suggestionItems.map((item) => (
              <EditorCommandItem
                value={item.title}
                onCommand={({
                  editor,
                  range,
                }: {
                  editor: EditorInstance;
                  range: { from: number; to: number };
                }) => {
                  if (item.command) {
                    item.command({ editor, range });
                  }
                }}
                className="flex w-full items-center space-x-2 px-2 py-1 text-left text-sm border-b-2 border-neutral-600 hover:bg-neutral-800 aria-selected:bg-blue-400 "
                key={item.title}
              >
                <div className="flex h-10 w-10 items-center justify-center rounded-md border border-muted bg-background">
                  {item.icon}
                </div>
                <div>
                  <p className="font-medium">{item.title}</p>
                  <p className="text-xs text-shadow-muted ">
                    {item.description}
                  </p>
                </div>
              </EditorCommandItem>
            ))}
          </EditorCommandList>
        </EditorCommand>
      </EditorContent>}
    </EditorRoot>
  );
};

export default NovelEditor;