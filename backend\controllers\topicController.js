const Topic = require("../models/Topic");
const Subject = require("../models/Subject");
const Question = require("../models/Question");
const { validationResult } = require("express-validator");

// Get all topics with enhanced statistics for admin
exports.getTopics = async (req, res) => {
  try {
    let query = {};
    const isAdmin = req.user && req.user.role === "admin";

    // Only admins see all topics; others see only active topics
    if (!isAdmin) {
      query.isActive = true;
    }

    // Use the enhanced getWithStats method for better performance
    const topics = await Topic.getWithStats(query, { sort: { createdAt: -1 } });

    let responseData = { topics };

    // Add aggregate statistics for admin users
    if (isAdmin) {
      const stats = await Topic.getAggregateStats();
      responseData.stats = stats;
    }

    res.json({ success: true, data: responseData });
  } catch (error) {
    console.error("Get topics error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching topics" }],
    });
  }
};

// Get topic by ID with enhanced statistics
exports.getTopic = async (req, res) => {
  try {
    const topic = await Topic.findById(req.params.id).populate(
      "subject",
      "name"
    );

    if (!topic) {
      return res
        .status(404)
        .json({ success: false, errors: [{ msg: "Topic not found" }] });
    }

    // Refresh stats if needed and user is admin
    const isAdmin = req.user && req.user.role === "admin";
    if (isAdmin) {
      await topic.refreshStatsIfNeeded();

      // Add individual question statistics for admin
      const questions = await Question.find({ topic: topic._id }).populate(
        "topic",
        "name"
      );
      topic.stats = {
        ...topic.stats,
        totalQuestions: questions.length,
        freeQuestions: questions.filter((q) => q.tier === "free").length,
        premiumQuestions: questions.filter((q) => q.tier === "premium").length,
      };
    }

    res.json({ success: true, data: topic });
  } catch (error) {
    console.error("Get topic error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching topic" }],
    });
  }
};

// Create topic
exports.createTopic = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }
    const {
      topicName,
      descriptionJson,
      descriptionHtml,
      subject,
      isActive,
      isPremium,
    } = req.body;
    // Check if subject exists
    const subjectExists = await Subject.findById(subject);
    if (!subjectExists) {
      return res
        .status(400)
        .json({ success: false, errors: [{ msg: "Invalid subject" }] });
    }
    // Check for duplicate topic name under the same subject
    const existing = await Topic.findOne({ topicName, subject });
    if (existing) {
      return res.status(400).json({
        success: false,
        errors: [
          { msg: "Topic with this name already exists for this subject" },
        ],
      });
    }
    const topic = new Topic({
      topicName,
      descriptionJson,
      descriptionHtml,
      subject,
      isActive,
      isPremium,
    });
    await topic.save();
    // Return updated subject with populated topics
    const updatedSubject = await Subject.findById(subject).populate("topics");
    res
      .status(201)
      .json({ success: true, data: { topic, subject: updatedSubject } });
  } catch (error) {
    console.error("Create topic error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while creating topic" }],
    });
  }
};

// Update topic
exports.updateTopic = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }
    const {
      topicName,
      descriptionJson,
      descriptionHtml,
      subject,
      isActive,
      isPremium,
    } = req.body;
    const topic = await Topic.findById(req.params.id);
    if (!topic) {
      return res
        .status(404)
        .json({ success: false, errors: [{ msg: "Topic not found" }] });
    }
    let subjectId = topic.subject;
    if (subject && subject !== topic.subject.toString()) {
      const subjectExists = await Subject.findById(subject);
      if (!subjectExists) {
        return res
          .status(400)
          .json({ success: false, errors: [{ msg: "Invalid subject" }] });
      }
      topic.subject = subject;
      subjectId = subject;
    }
    if (topicName) topic.topicName = topicName;
    if (descriptionJson) topic.descriptionJson = descriptionJson;
    if (descriptionHtml) topic.descriptionHtml = descriptionHtml;
    if (typeof isActive === "boolean") topic.isActive = isActive;
    if (typeof isPremium === "boolean") topic.isPremium = isPremium;
    await topic.save();
    // Return updated subject with populated topics
    const updatedSubject = await Subject.findById(subjectId).populate("topics");
    res.json({ success: true, data: { topic, subject: updatedSubject } });
  } catch (error) {
    console.error("Update topic error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while updating topic" }],
    });
  }
};

// Delete topic (soft delete)
exports.deleteTopic = async (req, res) => {
  try {
    const topic = await Topic.findByIdAndDelete(req.params.id);
    if (!topic) {
      return res
        .status(404)
        .json({ success: false, errors: [{ msg: "Topic not found" }] });
    }
    // Return updated subject with populated topics
    const updatedSubject = await Subject.findById(topic.subject).populate(
      "topics"
    );
    res.json({
      success: true,
      data: { msg: "Topic deleted successfully", subject: updatedSubject },
    });
  } catch (error) {
    console.error("Delete topic error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while deleting topic" }],
    });
  }
};
