"use server";

import axios from "axios";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { NextResponse } from "next/server";

const API_URL = process.env.NEXT_API_URL || "http://localhost:5000/api";

// Helper function to get authentication status and tokens
export async function getAuthData() {
  const cookieStore = await cookies();
  const isLoggedInCookie = cookieStore.get("isLoggedIn");
  const tokenCookie = cookieStore.get("token");
  const adminTokenCookie = cookieStore.get("adminToken");

  const isLoggedIn = isLoggedInCookie?.value === "true";
  const token = tokenCookie?.value;
  const adminToken = adminTokenCookie?.value;

  return { isLoggedIn, token, adminToken };
}

// Helper function to get token from cookies
export async function getAuthToken(type = "user"): Promise<string | null> {
  const cookieStore = await cookies();
  const token =
    type === "user"
      ? cookieStore.get("token")?.value
      : cookieStore.get("adminToken")?.value;
  return token || null;
}

// Helper function to get refresh token from cookies
export async function getRefreshToken(type = "user"): Promise<string | null> {
  const cookieStore = await cookies();
  const refreshToken =
    type === "user"
      ? cookieStore.get("refreshToken")?.value
      : cookieStore.get("adminRefreshToken")?.value;
  return refreshToken || null;
}

// Helper function to check authentication
export async function checkAuth(requireAdmin = false) {
  const { isLoggedIn, token, adminToken } = await getAuthData();

  if (!isLoggedIn) {
    return {
      isAuthenticated: false,
      response: NextResponse.json(
        {
          success: false,
          errors: [{ msg: "Unauthorized - Authentication required" }],
        },
        { status: 401 }
      ),
    };
  }

  const authToken = requireAdmin ? adminToken : token || adminToken;

  if (!authToken) {
    return {
      isAuthenticated: false,
      response: NextResponse.json(
        {
          success: false,
          errors: [
            {
              msg: requireAdmin
                ? "Admin authentication required"
                : "Authentication token required",
            },
          ],
        },
        { status: 401 }
      ),
    };
  }

  return {
    isAuthenticated: true,
    token: authToken,
  };
}

// Helper function to create authenticated axios instance
export async function createAuthenticatedApi() {
  const token = await getAuthToken();

  return axios.create({
    baseURL: API_URL,
    headers: {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });
}

// Helper function to create authenticated axios instance for admin
export async function createAuthenticatedAdminApi() {
  const token = await getAuthToken("admin");

  return axios.create({
    baseURL: API_URL,
    headers: {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });
}

// Helper function to set authentication cookies
export async function setAuthCookies(
  token: string,
  isAdmin = false,
  refreshToken?: string
) {
  const cookieStore = await cookies();

  if (isAdmin) {
    cookieStore.set("adminToken", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 24 * 60 * 60, // 24 hours
    });

    if (refreshToken) {
      cookieStore.set("adminRefreshToken", refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 7 * 24 * 60 * 60, // 7 days
      });
    }
  } else {
    cookieStore.set("token", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 30 * 24 * 60 * 60, // 30 days
    });
  }

  cookieStore.set("isLoggedIn", "true", {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
    maxAge: isAdmin ? 24 * 60 * 60 : 30 * 24 * 60 * 60,
  });
}

// Helper function to clear authentication cookies
export async function clearAuthCookies(isAdmin = false) {
  const cookieStore = await cookies();

  if (isAdmin) {
    cookieStore.delete("adminToken");
    cookieStore.delete("adminRefreshToken");
  } else {
    cookieStore.delete("token");
  }

  cookieStore.delete("isLoggedIn");
}

// Helper function to get API URL (since we can't export constants in server actions)
export async function getApiUrl(): Promise<string> {
  return API_URL;
}

// Helper function to handle errors in server actions (returns plain objects, not NextResponse)
export async function handleServerActionError(
  error: Error,
  defaultMessage = "An error occurred"
) {
  if (axios.isAxiosError(error)) {
    if(error.response?.status === 401){
      await clearAuthCookies(true);
      // return {
      //   success: false as const,
      //   errors: [{ msg: "Session expired. Please login again." }],
      // };
      redirect("/dashboard/login");
    }
    return {
      success: false as const,
      errors: error.response?.data?.errors || [
        { msg: error.message || defaultMessage },
      ],
    };
  }

  return {
    success: false as const,
    errors: [{ msg: error.message || defaultMessage }],
  };
}

export async function handleRedirectError(error: Error) {
  if (
    typeof error === "object" &&
    error !== null &&
    "digest" in error &&
    typeof (error as { digest?: unknown }).digest === "string" &&
    (error as { digest?: unknown }).digest &&
    (error as { digest: string }).digest.startsWith("NEXT_REDIRECT")
  ) {
    throw error;
  }
}

// Admin refresh token function
export async function refreshAdminToken(): Promise<{
  success: boolean;
  token?: string;
  refreshToken?: string;
  error?: string;
}> {
  try {
    const currentRefreshToken = await getRefreshToken("admin");

    if (!currentRefreshToken) {
      return {
        success: false,
        error: "No refresh token available"
      };
    }

    const apiUrl = await getApiUrl();
    const response = await axios.post(`${apiUrl}/auth/admin/refresh`, {
      refreshToken: currentRefreshToken
    });

    if (response.data.success) {
      const { token, refreshToken } = response.data.data;

      // Update cookies with new tokens
      await setAuthCookies(token, true, refreshToken);

      return {
        success: true,
        token,
        refreshToken
      };
    }

    return {
      success: false,
      error: response.data.errors?.[0]?.msg || "Failed to refresh token"
    };
  } catch (error) {
    console.error("Admin token refresh error:", error);

    if (axios.isAxiosError(error)) {
      return {
        success: false,
        error: error.response?.data?.errors?.[0]?.msg || "Token refresh failed"
      };
    }

    return {
      success: false,
      error: "Network error during token refresh"
    };
  }
}

// Helper function to create authenticated admin API with automatic token refresh
export async function createAuthenticatedAdminApiWithRefresh() {
  let token = await getAuthToken("admin");

  const api = axios.create({
    baseURL: API_URL,
    headers: {
      "Content-Type": "application/json",
    },
  });

  // Request interceptor to add token
  api.interceptors.request.use(
    (config) => {
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor to handle token refresh
  api.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;

      if (
        error.response?.status === 401 &&
        !originalRequest._retry &&
        error.response?.data?.errors?.[0]?.msg?.includes("token")
      ) {
        originalRequest._retry = true;

        const refreshResult = await refreshAdminToken();

        if (refreshResult.success && refreshResult.token) {
          token = refreshResult.token;
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return api(originalRequest);
        } else {
          // Refresh failed, clear cookies and redirect to login
          await clearAuthCookies(true);
          throw new Error("Session expired. Please login again.");
        }
      }

      return Promise.reject(error);
    }
  );

  return api;
}
