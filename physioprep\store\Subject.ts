import { getSubjectsForDropdown } from '@/actions/questions';
import { Subject } from "@/types/types";
import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";

// Base atoms
const SubjectAtom = atom<Subject | null>(null);
const SubjectsAtom = atomWithStorage<Subject[]>("Subjects", []);

// Atom to get a specific Subject by ID from the Subjects list
const getSubjectByIdAtom = atom(null, (get, set, id: string) => {
  const Subjects = get(SubjectsAtom);
  const subject = Subjects.find((Subject) => Subject._id === id) ?? null;
  set(SubjectAtom, subject);
  return subject;
});

const fetchSubjectsForDropdownAtom = atom(null, async (get, set) => {
  const res = await getSubjectsForDropdown();
  if (res.success) {
    set(SubjectsAtom, res.data || []);
  } else {
    // Handle error case - could log error or set empty array
    console.error('Failed to fetch subjects:', res.errors);
    set(SubjectsAtom, []);
  }
});

export { SubjectAtom, SubjectsAtom, getSubjectByIdAtom , fetchSubjectsForDropdownAtom };
