"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Edit, Trash2, Eye, ArrowUpDown } from "lucide-react";
import { Topic } from "@/types/types";
import Link from "next/link";
import { format } from "date-fns";

interface TopicActionsProps {
  topic: Topic;
  onDelete: (id: string) => void;
}

function TopicActions({ topic, onDelete }: TopicActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href={`/dashboard/topics/${topic._id}`}>
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/dashboard/topics/${topic._id}/edit`}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => onDelete(topic._id)}
          className="text-red-600 focus:text-red-600"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export const createTopicColumns = (
  onDelete: (id: string) => void
): ColumnDef<Topic>[] => [
  {
    accessorKey: "index",
    header: "No.",
    cell: ({ row }) => row.index + 1, // Add 1 to make it 1-based index
  },
  {
    accessorKey: "topicName",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Topic Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const topic = row.original;
      return (
        <div>
          <div className="font-medium">{topic.topicName}</div>
        </div>
      );
    },
  },
  {
    accessorKey: "subject",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Subject
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const subject = row.original.subject;
      return (
        <div className="font-medium">
          {typeof subject === 'object' ? subject.name : subject}
        </div>
      );
    },
  },
  {
    accessorKey: "isActive",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const isActive = row.getValue("isActive") as boolean;
      return (
        <Badge variant={isActive ? "default" : "secondary"}>
          {isActive ? "Active" : "Inactive"}
        </Badge>
      );
    },
  },
  {
    accessorKey: "isPremium",
    header: "Tier",
    cell: ({ row }) => {
      const isPremium = row.getValue("isPremium") as boolean;
      return (
        <Badge variant={isPremium ? "destructive" : "outline"}>
          {isPremium ? "Premium" : "Free"}
        </Badge>
      );
    },
  },
  {
    accessorKey: "stats",
    header: "Questions",
    cell: ({ row }) => {
      const topic = row.original as Topic;
      const totalQuestions = (topic).stats?.totalQuestions || 0;
      const freeQuestions = (topic).stats?.freeQuestions || 0;
      const premiumQuestions = (topic).stats?.premiumQuestions || 0;
      
      return (
        <div className="text-sm">
          <div className="font-medium">{totalQuestions} total</div>
          <div className="text-muted-foreground">
            {freeQuestions} free, {premiumQuestions} premium
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Created
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const createdAt = row.getValue("createdAt") as string;
      return (
        <div className="text-sm text-muted-foreground">
          {format(new Date(createdAt), "MMM dd, yyyy")}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const topic = row.original;
      return <TopicActions topic={topic} onDelete={onDelete} />;
    },
  },
];