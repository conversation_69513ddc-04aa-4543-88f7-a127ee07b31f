"use client";

import { useActionState, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

import {
  createTopic,
  updateTopic,
  TopicFormState,
  getSubjectsForDropdown,
} from "../actions";
import { Topic, Subject } from "@/types/types";
import { ArrowLeft, Save, FileText, Smartphone } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { generateHTML } from "@tiptap/html";

import NovelEditor from "@/components/editor/NovelEditor";
import { JSONContent } from "novel";
import { defaultExtensions } from "@/components/editor/extentions";

interface TopicFormProps {
  topic?: Topic;
  mode: "create" | "edit";
}

export function TopicForm({ topic, mode }: TopicFormProps) {
  const router = useRouter();
  const isEdit = mode === "edit";
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [descriptionJson, setDescriptionJson] = useState<JSONContent>(() => {
    // Initialize description with better error handling
    if (topic?.descriptionJson) {
      try {
        return typeof topic.descriptionJson === "string"
          ? JSON.parse(topic.descriptionJson)
          : topic.descriptionJson;
      } catch (error) {
        console.error("Failed to parse topic descriptionJson:", error);
        // Fallback to plain text if JSON parsing fails
        return {
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text:
                    typeof topic.descriptionJson === "string"
                      ? topic.descriptionJson
                      : "",
                },
              ],
            },
          ],
        };
      }
    }

    // Default empty content
    return {
      type: "doc",
      content: [
        {
          type: "paragraph",
          content: [],
        },
      ],
    };
  });

  const [loadingSubjects, setLoadingSubjects] = useState(true);
  const [isActive, setIsActive] = useState(topic?.isActive ?? true);
  const [isPremium, setIsPremium] = useState(topic?.isPremium ?? false);

  const action =
    isEdit && topic ? updateTopic.bind(null, topic._id) : createTopic;

  const [state, formAction, isPending] = useActionState<
    TopicFormState,
    FormData
  >(action, { error: null, success: false });


  // Fetch subjects for dropdown
  useEffect(() => {
    async function fetchSubjects() {
      try {
        const result = await getSubjectsForDropdown();
        console.log(result);
        if (result.success) {
          setSubjects(result.data);
        } else {
          toast.error(result.errors[0]?.msg);
        }
      } catch {
        toast.error("Failed to load subjects");
      } finally {
        setLoadingSubjects(false);
      }
    }

    fetchSubjects();
  }, []);

  // Handle form success
  useEffect(() => {
    if (state?.success) {
      toast.success(
        isEdit ? "Topic updated successfully!" : "Topic created successfully!"
      );

      // Navigate back to topics list
      router.push("/dashboard/topics");
    }
  }, [state?.success, state?.data, isEdit, router]);

  const handleSubmit = (formData: FormData) => {
    console.log("Form data:", formData);
    try {

      const descriptionjson = JSON.stringify(descriptionJson)
      const descriptionHtml = getPlainTextPreview(descriptionJson);
      
      formData.set("descriptionJson", descriptionjson);
      formData.set("descriptionHtml", descriptionHtml);


      // Add metadata for mobile app consumption

      // Add switch states to form data
      formData.set("isActive", isActive ? "on" : "off");
      formData.set("isPremium", isPremium ? "on" : "off");

      console.log("Submitting topic with description:", {
        isActive,
        isPremium,
      });

      formAction(formData);
    } catch (error) {
      console.error("Error preparing form data:", error);
      toast.error("Failed to prepare form data. Please try again.");
    }
  };

  // Helper function to convert rich content to plain text for mobile preview
  const getPlainTextPreview = (content: JSONContent): string => {
    if (!content.content) return "";

    const html = generateHTML(content, defaultExtensions);
    return html;
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/topics">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Topics
          </Link>
        </Button>
      </div>

      <div>
        <h2 className="text-3xl font-bold tracking-tight">
          {isEdit ? "Edit Topic" : "Create Topic"}
        </h2>
        <p className="text-muted-foreground">
          {isEdit
            ? "Update the topic information below."
            : "Add a new topic to the system."}
        </p>
      </div>

      <Card className="max-w-6xl shadow-sm">
        <CardHeader className="pb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <FileText className="w-5 h-5 text-primary" />
            </div>
            <div>
              <CardTitle className="text-xl">
                {isEdit ? "Edit Topic" : "Create New Topic"}
              </CardTitle>
              <CardDescription className="mt-1">
                {isEdit
                  ? "Make changes to the topic information."
                  : "Fill in the details to create a new topic."}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <Separator />
        <CardContent className="pt-2">
          {state?.error && (
            <div className="mb-6 p-4 text-sm text-red-700 bg-red-50 border border-red-200 rounded-lg flex items-center gap-3">
              <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0"></div>
              <span>{state.error}</span>
            </div>
          )}

          <form action={handleSubmit} className="space-y-8">
            {/* Basic Information Section */}
            <div className="space-y-6">
              <div className="border-b border-border pb-4">
                <h3 className="text-lg font-medium text-foreground">
                  Basic Information
                </h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Enter the basic details for the topic.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="topicName" className="text-sm font-medium">
                    Topic Name *
                  </Label>
                  <Input
                    id="topicName"
                    name="topicName"
                    placeholder="Enter topic name"
                    defaultValue={topic?.topicName || ""}
                    required
                    disabled={isPending}
                    className="h-10"
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="subject" className="text-sm font-medium">
                    Subject *
                  </Label>
                  <Select
                    name="subject"
                    defaultValue={
                      typeof topic?.subject === "object"
                        ? topic.subject._id
                        : topic?.subject || ""
                    }
                    disabled={loadingSubjects || isPending}
                    required
                  >
                    <SelectTrigger className="h-10">
                      <SelectValue placeholder="Select a subject" />
                    </SelectTrigger>
                    <SelectContent>
                      {subjects.map((subject) => (
                        <SelectItem key={subject._id} value={subject._id}>
                          {subject.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Description Section */}
            <div className="space-y-6">
              <div className="border-b border-border pb-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-foreground">
                      Description
                    </h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Add rich content description for the topic.
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">
                      <FileText className="w-3 h-3 mr-1" />
                      Rich Text
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      <Smartphone className="w-3 h-3 mr-1" />
                      Mobile Ready
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="border rounded-lg min-h-[350px] max-w-md overflow-hidden">
                <NovelEditor data={descriptionJson} setData={setDescriptionJson} />
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <div className="p-1 bg-blue-100 rounded">
                    <Smartphone className="w-4 h-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-blue-900 mb-2">
                      Mobile App Compatibility
                    </h4>
                    <p className="text-xs text-blue-700 mb-3">
                      Rich content (tables, formatting, etc.) will be
                      automatically converted to readable text in the mobile
                      app. Basic formatting like <strong>bold</strong>,{" "}
                      <em>italic</em>, and lists will be preserved.
                    </p>
                    {getPlainTextPreview(descriptionJson) && (
                      <div className="bg-white border border-blue-200 rounded p-3 max-w-max">
                        <p className="text-xs font-medium text-blue-900 mb-2">
                          Mobile Preview:
                        </p>
                        <div
                        className="max-w-md"
                          dangerouslySetInnerHTML={{
                            __html: getPlainTextPreview(descriptionJson),
                          }}
                        />
                        
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Settings Section */}
            <div className="space-y-6">
              <div className="border-b border-border pb-4">
                <h3 className="text-lg font-medium text-foreground">
                  Settings
                </h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Configure topic visibility and access settings.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label htmlFor="isActive" className="text-sm font-medium">
                      Active Status
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Make this topic visible to users
                    </p>
                  </div>
                  <Switch
                    id="isActive"
                    checked={isActive}
                    onCheckedChange={setIsActive}
                    disabled={isPending}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label htmlFor="isPremium" className="text-sm font-medium">
                      Premium Content
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Require premium access to view
                    </p>
                  </div>
                  <Switch
                    id="isPremium"
                    checked={isPremium}
                    onCheckedChange={setIsPremium}
                    disabled={isPending}
                  />
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-border">
              <Button
                type="button"
                variant="outline"
                asChild
                disabled={isPending}
              >
                <Link href="/dashboard/topics">Cancel</Link>
              </Button>
              <Button
                type="submit"
                disabled={isPending || loadingSubjects}
                className="min-w-[120px]"
              >
                {isPending ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    {isEdit ? "Updating..." : "Creating..."}
                  </div>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    {isEdit ? "Update Topic" : "Create Topic"}
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
