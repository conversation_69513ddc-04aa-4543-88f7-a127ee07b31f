import { getTopics } from "@/app/dashboard/topics/actions";
import { Topic } from "@/types/types";
import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";

// Base atoms
const topicAtom = atom<Topic | null>(null);
const topicsAtom = atomWithStorage<Topic[]>("topics", []);

// Atom to get a specific topic by ID from the topics list
const getTopicByIdAtom = atom(null, (get, set, id: string) => {
  const topics = get(topicsAtom);
  set(topicAtom, topics.find((topic) => topic._id === id) ?? null);
});

const fetchTopicsForDropdownAtom = atom(null, async (get, set) => {
  const res = await getTopics();
  if (res.success) {
    set(topicsAtom, res.data.topics);
  } else {
    // Handle error case - could log error or set empty array
    console.error('Failed to fetch topics:', res.errors);
    set(topicsAtom, []);
  }
});


export {
  topicAtom,
  topicsAtom,
  getTopicByIdAtom,
  fetchTopicsForDropdownAtom,
  };
