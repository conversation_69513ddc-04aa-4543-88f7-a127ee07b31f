const Question = require("../models/Question");
const { asyncHandler } = require("../middleware/errorHandler");
const Topic = require("../models/Topic");

// @desc    Get all questions with admin features
// @route   GET /api/questions
// @access  Private
const getQuestions = asyncHandler(async (req, res) => {
  const isAdmin = req.user && req.user.role === 'admin';

  // Extract query parameters
  const {
    page = 1,
    limit = 10,
    search,
    subject,
    topic,
    difficulty,
    tier,
    isActive,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query;

  // Build filter object
  let filter = {};

  // For non-admin users, only show active questions
  if (!isAdmin) {
    filter.isActive = true;
  } else if (isActive !== undefined) {
    // Admin can filter by active status
    filter.isActive = isActive === 'true';
  }

  // Apply filters
  if (subject) filter.subject = subject;
  if (topic) filter.topic = topic;
  if (difficulty) filter.difficulty = difficulty;
  if (tier) filter.tier = tier;

  // Search functionality (admin only)
  if (search && isAdmin) {
    filter.$or = [
      { text: { $regex: search, $options: 'i' } },
      { 'options.text': { $regex: search, $options: 'i' } }
    ];
  }

  // Build sort object
  const sortObj = {};
  const validSortFields = ['createdAt', 'updatedAt', 'difficulty', 'tier', 'usageCount', 'correctAnswerCount'];
  const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
  const sortDirection = sortOrder === 'asc' ? 1 : -1;
  sortObj[sortField] = sortDirection;

  // Calculate pagination
  const pageNum = Math.max(1, parseInt(page));
  const limitNum = Math.min(100, Math.max(1, parseInt(limit)));
  const skip = (pageNum - 1) * limitNum;

  try {
    // Get total count for pagination
    const totalQuestions = await Question.countDocuments(filter);

    // Get questions with pagination
    const questions = await Question.find(filter)
      .populate('topic', 'topicName')
      .populate('subject', 'name')
      .sort(sortObj)
      .skip(skip)
      .limit(limitNum)
      .lean();

    // Calculate pagination info
    const totalPages = Math.ceil(totalQuestions / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;

    // Prepare response data
    const responseData = {
      questions,
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalQuestions,
        limit: limitNum,
        hasNextPage,
        hasPrevPage
      }
    };

    // Add admin-only statistics
    if (isAdmin) {
      const stats = await Question.aggregate([
        { $match: filter },
        {
          $group: {
            _id: null,
            totalQuestions: { $sum: 1 },
            activeQuestions: { $sum: { $cond: ['$isActive', 1, 0] } },
            inactiveQuestions: { $sum: { $cond: ['$isActive', 0, 1] } },
            freeQuestions: { $sum: { $cond: [{ $eq: ['$tier', 'free'] }, 1, 0] } },
            premiumQuestions: { $sum: { $cond: [{ $eq: ['$tier', 'premium'] }, 1, 0] } },
            easyQuestions: { $sum: { $cond: [{ $eq: ['$difficulty', 'easy'] }, 1, 0] } },
            mediumQuestions: { $sum: { $cond: [{ $eq: ['$difficulty', 'medium'] }, 1, 0] } },
            hardQuestions: { $sum: { $cond: [{ $eq: ['$difficulty', 'hard'] }, 1, 0] } },
            avgUsageCount: { $avg: '$usageCount' },
            avgCorrectAnswerCount: { $avg: '$correctAnswerCount' }
          }
        }
      ]);

      responseData.stats = stats[0] || {
        totalQuestions: 0,
        activeQuestions: 0,
        inactiveQuestions: 0,
        freeQuestions: 0,
        premiumQuestions: 0,
        easyQuestions: 0,
        mediumQuestions: 0,
        hardQuestions: 0,
        avgUsageCount: 0,
        avgCorrectAnswerCount: 0
      };
    }

    res.json({
      success: true,
      data: responseData,
    });
  } catch (error) {
    console.error('Get questions error:', error);
    res.status(500).json({
      success: false,
      errors: [{ msg: 'Server error while fetching questions' }],
    });
  }
});

// @desc    Get question by ID
// @route   GET /api/questions/:id
// @access  Private
const getQuestionById = asyncHandler(async (req, res) => {
  const question = await Question.findById(req.params.id).populate("topic");

  if (!question) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Question not found" }],
    });
  }

  res.json({
    success: true,
    data: question,
  });
});

// @desc    Create new question
// @route   POST /api/questions
// @access  Private/Admin
const createQuestion = asyncHandler(async (req, res) => {
  const {
    text,
    options,
    explanationJson,
    explanationHtml,
    topic,
    difficulty,
    tier,
    isActive
  } = req.body;

  const topicDoc = await Topic.findById(topic);
  if (!topicDoc) {
    return res.status(400).json({
      success: false,
      errors: [{ msg: "Topic not found" }],
    });
  }

  const subject = topicDoc.subject;
  const question = await Question.create({
    text,
    options,
    explanationJson,
    explanationHtml,
    topic,
    subject,
    difficulty,
    tier,
    isActive,
    createdBy: req.user._id,
  });

  // Update topic statistics immediately for admin users
  if (req.user.role === 'admin') {
    await topicDoc.calculateStats();
  }

  const questions = await Question.find({ topic: question.topic }).populate(
    "topic"
  );
  res.status(201).json({
    success: true,
    data: questions,
  });
});

// @desc    Update question
// @route   PUT /api/questions/:id
// @access  Private/Admin
const updateQuestion = asyncHandler(async (req, res) => {
  const {
    text,
    options,
    explanationJson,
    explanationHtml,
    topic,
    difficulty,
    tier,
    isActive
  } = req.body;

  // Build update object with only provided fields
  const updateData = {};
  if (text !== undefined) updateData.text = text;
  if (options !== undefined) updateData.options = options;
  if (explanationJson !== undefined) updateData.explanationJson = explanationJson;
  if (explanationHtml !== undefined) updateData.explanationHtml = explanationHtml;
  if (topic !== undefined) updateData.topic = topic;
  if (difficulty !== undefined) updateData.difficulty = difficulty;
  if (tier !== undefined) updateData.tier = tier;
  if (isActive !== undefined) updateData.isActive = isActive;

  const question = await Question.findByIdAndUpdate(req.params.id, updateData, {
    new: true,
    runValidators: true,
  });

  if (!question) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Question not found" }],
    });
  }

  // Update topic statistics immediately for admin users
  if (req.user.role === 'admin') {
    const topicDoc = await Topic.findById(question.topic);
    if (topicDoc) {
      await topicDoc.calculateStats();
    }
  }

  const questions = await Question.find({ topic: question.topic }).populate(
    "topic",
    "name"
  );
  res.json({
    success: true,
    data: questions,
  });
});

// @desc    Delete question
// @route   DELETE /api/questions/:id
// @access  Private/Admin
const deleteQuestion = asyncHandler(async (req, res) => {
  const question = await Question.findById(req.params.id);

  if (!question) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Question not found" }],
    });
  }

  const topicId = question.topic;
  await question.deleteOne(); // Use deleteOne instead of findByIdAndDelete

  // Update topic statistics immediately for admin users
  if (req.user.role === 'admin') {
    const topic = await Topic.findById(topicId);
    if (topic) {
      await topic.calculateStats();
    }
  }

  const questions = await Question.find({ topic: topicId }).populate(
    "topic",
    "name"
  );

  res.json({
    success: true,
    data: questions,
  });
});

// @desc    Get questions by topic
// @route   GET /api/questions/topic/:topicId
// @access  Private
const getQuestionsByTopic = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    search = "",
    difficulty = "",
    tier = "",
    sortBy = "createdAt",
    sortOrder = "desc",
  } = req.query;

  const filter = { topic: req.params.topicId };
  if (difficulty) filter.difficulty = difficulty;
  if (tier) filter.tier = tier;
  if (search) {
    filter.$or = [
      { text: { $regex: search, $options: "i" } },
      { "options.text": { $regex: search, $options: "i" } },
    ];
  }

  const sort = {};
  sort[sortBy] = sortOrder === "asc" ? 1 : -1;

  const skip = (parseInt(page) - 1) * parseInt(limit);

  const [questions, total] = await Promise.all([
    Question.find(filter)
      .populate("topic", "name")
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit)),
    Question.countDocuments(filter),
  ]);

  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.json({
    success: true,
    data: {
      questions,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalQuestions: total,
        hasNextPage,
        hasPrevPage,
        limit: parseInt(limit),
      },
    },
  });
});

// @desc    Get random questions
// @route   GET /api/questions/random/:count
// @access  Private
const getRandomQuestions = asyncHandler(async (req, res) => {
  const count = parseInt(req.params.count);
  const questions = await Question.aggregate([
    { $sample: { size: count } },
    {
      $lookup: {
        from: "topics",
        localField: "topic",
        foreignField: "_id",
        as: "topic",
      },
    },
    {
      $project: {
        correctAnswer: 0,
      },
    },
  ]);

  res.json({
    success: true,
    data: questions,
  });
});

// Get all questions with filtering and pagination
const getQuestionsFiltered = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    topic,
    category,
    difficulty,
    tier,
    search,
    sortBy = "createdAt",
    sortOrder = "desc",
  } = req.query;

  // Build filter object
  const filter = { isActive: true };

  if (topic) filter.topic = topic;
  if (category) filter.category = category;
  if (difficulty) filter.difficulty = difficulty;

  // Apply tier filtering based on user's premium status
  if (tier) {
    filter.tier = tier;
  } else if (req.allowedTiers) {
    filter.tier = { $in: req.allowedTiers };
  }

  // Add search functionality
  if (search) {
    filter.$or = [
      { questionText: { $regex: search, $options: "i" } },
      { explanation: { $regex: search, $options: "i" } },
      { tags: { $in: [new RegExp(search, "i")] } },
    ];
  }

  // Build sort object
  const sort = {};
  sort[sortBy] = sortOrder === "asc" ? 1 : -1;

  // Calculate pagination
  const skip = (page - 1) * limit;

  // Execute query
  const [questions, total] = await Promise.all([
    Question.find(filter)
      .populate("topic", "name slug color")
      .populate("createdBy", "name")
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean(),
    Question.countDocuments(filter),
  ]);

  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    success: true,
    data: {
      questions,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalQuestions: total,
        hasNextPage,
        hasPrevPage,
        limit: parseInt(limit),
      },
    },
  });
});

// Get single question by ID
const getQuestion = asyncHandler(async (req, res) => {
  const question = await Question.findById(req.params.id)
    .populate("topic", "name slug color")
    .populate("createdBy", "name");

  if (!question) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Question not found" }],
    });
  }

  // Check if user has access to this question tier
  if (req.allowedTiers && !req.allowedTiers.includes(question.tier)) {
    return res.status(403).json({
      success: false,
      errors: [
        { msg: "Premium subscription required to access this question" },
      ],
    });
  }

  res.status(200).json({
    success: true,
    data: { question },
  });
});

// Submit answer to question
const submitAnswer = asyncHandler(async (req, res) => {
  const { selectedOptionId, timeSpent = 0 } = req.body;

  const question = await Question.findById(req.params.id);

  if (!question) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "Question not found" }],
    });
  }

  // Check if user has access to this question tier
  if (req.allowedTiers && !req.allowedTiers.includes(question.tier)) {
    return res.status(403).json({
      success: false,
      errors: [
        { msg: "Premium subscription required to access this question" },
      ],
    });
  }

  // Check if selected option exists
  const selectedOption = question.options.id(selectedOptionId);
  if (!selectedOption) {
    return res.status(400).json({
      success: false,
      errors: [{ msg: "Invalid option selected" }],
    });
  }

  // Check if answer is correct
  const isCorrect = selectedOption.isCorrect;
  const correctAnswer = question.getCorrectAnswer();

  // Update question stats
  if (typeof question.recordAttempt === 'function') {
    await question.recordAttempt(isCorrect, typeof timeSpent === 'number' ? timeSpent : 0);
  }

  // Update user stats if authenticated
  if (req.user) {
    req.user.stats.totalQuestionsAnswered += 1;
    if (isCorrect) {
      req.user.stats.correctAnswers += 1;
    }

    // Record detailed attempt for analytics
    try {
      await req.user.recordQuestionAttempt(
        question._id,
        isCorrect,
        question.subject || null,
        typeof timeSpent === 'number' ? timeSpent : 0
      );
    } catch (e) {
      // Non-fatal: log and continue
      console.error('Failed to record question attempt:', e?.message || e);
    }

    // Recalculate average score
    req.user.stats.averageScore = Math.round(
      (req.user.stats.correctAnswers / req.user.stats.totalQuestionsAnswered) *
        100
    );

    // Update activity streak
    await req.user.updateStreak();
    await req.user.save();
  }

  res.status(200).json({
    success: true,
    data: {
      isCorrect,
      correctAnswer: {
        id: correctAnswer._id,
        text: correctAnswer.text,
      },
      explanation: question.explanation,
      userStats: req.user
        ? {
            totalAnswered: req.user.stats.totalQuestionsAnswered,
            correctAnswers: req.user.stats.correctAnswers,
            accuracyPercentage: req.user.accuracyPercentage,
          }
        : null,
    },
  });
});

// Get questions for quiz/test
const getQuestionsForQuiz = asyncHandler(async (req, res) => {
  const { topic, category, difficulty, tier, count = 10 } = req.query;

  // Build filters
  const filters = {
    limit: parseInt(count),
    excludeIds: [],
  };

  if (topic) filters.topic = topic;
  if (category) filters.category = category;
  if (difficulty) filters.difficulty = difficulty;

  // Apply tier filtering based on user's premium status
  if (tier) {
    filters.tier = tier;
  } else if (req.allowedTiers) {
    filters.tier = req.allowedTiers.includes("premium") ? null : "free";
  }

  const questions = await Question.getForQuizTest(filters);

  if (questions.length === 0) {
    return res.status(404).json({
      success: false,
      errors: [{ msg: "No questions found matching the criteria" }],
    });
  }

  res.status(200).json({
    success: true,
    data: { questions },
  });
});

// Bulk delete questions (Admin only)
const bulkDeleteQuestions = asyncHandler(async (req, res) => {
  const { questionIds } = req.body;

  const result = await Question.deleteMany({
    _id: { $in: questionIds },
    createdBy: req.user._id, // Only allow deleting own questions unless admin
  });

  res.status(200).json({
    success: true,
    data: {
      message: `${result.deletedCount} questions deleted successfully`,
      deletedCount: result.deletedCount,
    },
  });
});

// Bulk update questions (Admin only)
const bulkUpdateQuestions = asyncHandler(async (req, res) => {
  const { questionIds, updates } = req.body;

  const result = await Question.updateMany(
    { _id: { $in: questionIds } },
    updates,
    { runValidators: true }
  );

  res.status(200).json({
    success: true,
    data: {
      message: `${result.modifiedCount} questions updated successfully`,
      modifiedCount: result.modifiedCount,
    },
  });
});

// Get daily question
const getDailyQuestion = asyncHandler(async (req, res) => {
  try {
    // Get a random question that hasn't been used recently
    const question = await Question.aggregate([
      {
        $match: {
          isActive: true,
          tier: req.user.isPremiumActive()
            ? { $in: ["free", "premium"] }
            : "free",
        },
      },
      { $sample: { size: 1 } },
    ]);

    if (!question.length) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "No questions available" }],
      });
    }

    res.json({
      success: true,
      data: question[0],
    });
  } catch (error) {
    console.error("Get daily question error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching daily question" }],
    });
  }
});

module.exports = {
  getQuestions,
  getQuestionById,
  createQuestion,
  updateQuestion,
  deleteQuestion,
  getQuestionsByTopic,
  getRandomQuestions,
  getQuestionsFiltered,
  getQuestion,
  submitAnswer,
  getQuestionsForQuiz,
  bulkDeleteQuestions,
  bulkUpdateQuestions,
  getDailyQuestion,
};
