"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Copy, FileText, AlertCircle, CheckCircle } from "lucide-react";
import { toast } from "sonner";

interface QuestionOption {
  text: string;
  isCorrect: boolean;
}

interface BulkOptionsPasteProps {
  onOptionsUpdate: (options: QuestionOption[]) => void;
  currentOptions: QuestionOption[];
  maxOptions?: number;
}

export function BulkOptionsPaste({
  onOptionsUpdate,
  currentOptions,
  maxOptions = 6,
}: BulkOptionsPasteProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [pastedText, setPastedText] = useState("");
  const [previewOptions, setPreviewOptions] = useState<QuestionOption[]>([]);
  const [parseMode, setParseMode] = useState<"line" | "numbered" | "lettered">(
    "line"
  );

  useEffect(() => {
    setPreviewOptions(currentOptions);
  }, [currentOptions]);

  // Parse the pasted text into options
  const parseOptions = (
    text: string,
    mode: "line" | "numbered" | "lettered"
  ) => {
    if (!text.trim()) {
      setPreviewOptions([]);
      return;
    }

    const lines = text.split("\n").filter((line) => line.trim());
    let options: QuestionOption[] = [];

    switch (mode) {
      case "line":
        // Each line is an option
        options = lines.map((line) => ({
          text: line.trim(),
          isCorrect: false,
        }));
        break;

      case "numbered":
        // Format: "1. Option text" or "1) Option text"
        options = lines
          .map((line) => {
            const match = line.match(/^\d+[.)]\s*(.+)$/);
            return match
              ? {
                  text: match[1].trim(),
                  isCorrect: false,
                }
              : null;
          })
          .filter(Boolean) as QuestionOption[];
        break;

      case "lettered":
        // Format: "a. Option text" or "a) Option text" or "A. Option text"
        options = lines
          .map((line) => {
            const match = line.match(/^[a-zA-Z][.)]\s*(.+)$/);
            return match
              ? {
                  text: match[1].trim(),
                  isCorrect: false,
                }
              : null;
          })
          .filter(Boolean) as QuestionOption[];
        break;
    }

    // Limit to maxOptions
    if (options.length > maxOptions) {
      options = options.slice(0, maxOptions);
      toast.warning(`Only the first ${maxOptions} options will be used.`);
    }

    setPreviewOptions(options);
  };

  // Handle text change and auto-detect format
  const handleTextChange = (text: string) => {
    setPastedText(text);

    if (!text.trim()) {
      setPreviewOptions([]);
      return;
    }

    // Auto-detect format
    const lines = text.split("\n").filter((line) => line.trim());
    let detectedMode: "line" | "numbered" | "lettered" = "line";

    if (lines.some((line) => /^\d+[.)]\s*/.test(line))) {
      detectedMode = "numbered";
    } else if (lines.some((line) => /^[a-zA-Z][.)]\s*/.test(line))) {
      detectedMode = "lettered";
    }

    setParseMode(detectedMode);
    parseOptions(text, detectedMode);
  };

  // Apply the parsed options
  const handleApplyOptions = () => {
    if (previewOptions.length === 0) {
      toast.error("No valid options found to apply.");
      return;
    }

    if (previewOptions.length < 2) {
      toast.error("At least 2 options are required.");
      return;
    }

    // Merge with existing options or replace them
    const newOptions = [...previewOptions];

    // Ensure we have at least 2 options and at most maxOptions
    while (newOptions.length < 2) {
      newOptions.push({ text: "", isCorrect: false });
    }

    onOptionsUpdate(newOptions);
    setIsOpen(false);
    setPastedText("");
    setPreviewOptions([]);
    toast.success(`${previewOptions.length} options applied successfully!`);
  };

  // Get format examples
  const getFormatExamples = () => {
    const examples = {
      line: "Option 1\nOption 2\nOption 3\nOption 4",
      numbered: "1. Option 1\n2. Option 2\n3. Option 3\n4. Option 4",
      lettered: "a. Option 1\nb. Option 2\nc. Option 3\nd. Option 4",
    };
    return examples;
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button type="button" variant="outline" size="sm" className="gap-2">
          <Copy className="h-4 w-4" />
          Bulk Paste Options
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Bulk Paste Options
          </DialogTitle>
          <DialogDescription>
            Paste multiple options at once. The system will automatically detect
            the format and parse them.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Format Detection */}
          <div className="flex items-center gap-2">
            <Label className="text-sm font-medium">Detected Format:</Label>
            <Badge variant="secondary" className="capitalize">
              {parseMode === "line"
                ? "Line by Line"
                : parseMode === "numbered"
                ? "Numbered (1., 2., etc.)"
                : "Lettered (a., b., etc.)"}
            </Badge>
          </div>

          {/* Input Area */}
          <div className="space-y-2">
            <Label htmlFor="bulk-options">Paste Options Here</Label>
            <Textarea
              id="bulk-options"
              placeholder={`Paste your options here. Supported formats:

Line by Line:
${getFormatExamples().line}

Numbered:
${getFormatExamples().numbered}

Lettered:
${getFormatExamples().lettered}`}
              value={pastedText}
              onChange={(e) => handleTextChange(e.target.value)}
              className="min-h-[150px] font-mono text-sm"
            />
          </div>

          {/* Preview */}
          {previewOptions.length > 0 && (
            <>
              <Separator />
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Label className="text-sm font-medium">Preview:</Label>
                  <Badge variant="outline">
                    {previewOptions.length} option
                    {previewOptions.length !== 1 ? "s" : ""} found
                  </Badge>
                </div>

                <div className="space-y-2 max-h-[200px] overflow-y-auto">
                  {previewOptions.map((option, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-3 p-2 border rounded-md bg-muted/50"
                    >
                      <Badge variant="outline" className="text-xs">
                        {index + 1}
                      </Badge>
                      <span className="flex-1 text-sm">{option.text}</span>
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                  ))}
                </div>

                {/* Validation Messages */}
                {previewOptions.length < 2 && (
                  <div className="flex items-center gap-2 text-amber-600">
                    <AlertCircle className="h-4 w-4" />
                    <span className="text-sm">
                      At least 2 options are required.
                    </span>
                  </div>
                )}

                {previewOptions.length > maxOptions && (
                  <div className="flex items-center gap-2 text-amber-600">
                    <AlertCircle className="h-4 w-4" />
                    <span className="text-sm">
                      Only the first {maxOptions} options will be used.
                    </span>
                  </div>
                )}
              </div>
            </>
          )}

          {/* Help Text */}
          <div className="text-xs text-muted-foreground space-y-1">
            <p>
              <strong>Supported formats:</strong>
            </p>
            <ul className="list-disc list-inside space-y-1 ml-2">
              <li>
                <strong>Line by Line:</strong> Each line becomes an option
              </li>
              <li>
                <strong>Numbered:</strong> 1. Option, 2. Option, etc.
              </li>
              <li>
                <strong>Lettered:</strong> a. Option, b. Option, etc.
              </li>
            </ul>
            <p className="mt-2">
              <strong>Note:</strong> All options will be set as incorrect by
              default. You can mark the correct answer(s) after applying.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              setIsOpen(false);
              setPastedText("");
              setPreviewOptions([]);
            }}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleApplyOptions}
            disabled={previewOptions.length < 2}
            className="gap-2"
          >
            <CheckCircle className="h-4 w-4" />
            Apply {previewOptions.length} Options
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
