/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

export const Colors = {
  white2: 'rgb(230, 230, 230)',
  grey6: 'rgb(29, 30, 28)',
  grey5: 'rgb(48, 50, 47)',
  grey4: 'rgb(61, 63, 59)',
  grey3: 'rgb(81, 85, 79)',
  grey2: 'rgb(124, 129, 121)',
  grey: 'rgb(162, 167, 160)',
  background: 'rgb(246, 246, 248)',
  foreground: 'rgb(241, 240, 245)',
  root: 'rgb(2, 4, 1)',
  card: 'rgb(2, 4, 1)',
  destructive: 'rgb(254, 67, 54)',
  primary: 'rgb(107, 169, 253)',
  secondary: 'rgb(59, 130, 246)',
  accent: 'rgb(86, 218, 220)',
};

// export const Colors = {
//   light: {
//     text: '#11181C',
//     background: '#fff',
//     tint: tintColorLight,
//     icon: '#687076',
//     tabIconDefault: '#687076',
//     tabIconSelected: tintColorLight,
//   },
//   dark: {
//     text: '#ECEDEE',
//     background: '#151718',
//     tint: tintColorDark,
//     icon: '#9BA1A6',
//     tabIconDefault: '#9BA1A6',
//     tabIconSelected: tintColorDark,
//   },
// };
