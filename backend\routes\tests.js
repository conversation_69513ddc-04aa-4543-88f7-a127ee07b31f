const express = require("express");
const router = express.Router();
const { auth } = require("../middleware/auth");
const { startComprehensiveTest, submitComprehensiveTest } = require("../controllers/comprehensiveTestController");

// New dynamic comprehensive test endpoints
// No persistence: tests are generated on demand.
// Security: do not leak correct answers until submission.

// @route   POST /api/tests/comprehensive/start
// @desc    Start a comprehensive test across all subjects/topics
// @access  Private
router.post("/comprehensive/start", auth, startComprehensiveTest);

// @route   POST /api/tests/comprehensive/submit
// @desc    Submit answers and get review
// @access  Private
router.post("/comprehensive/submit", auth, submitComprehensiveTest);

module.exports = router;
