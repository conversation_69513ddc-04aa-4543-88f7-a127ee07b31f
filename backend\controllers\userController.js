const { validationResult } = require("express-validator");
const User = require("../models/User");
const Subject = require("../models/Subject");
const Question = require("../models/Question");
const mongoose = require("mongoose");

// Get all users (admin only)
exports.getUsers = async (req, res) => {
  try {
    const users = await User.find()
      .select(
        "-password -emailVerificationToken -passwordResetToken -passwordResetExpires"
      )
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: users,
    });
  } catch (error) {
    console.error("Get users error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching users" }],
    });
  }
};

// Get single user
exports.getUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select(
      "-password -emailVerificationToken -passwordResetToken -passwordResetExpires"
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "User not found" }],
      });
    }

    res.json({
      success: true,
      data: user,
    });
  } catch (error) {
    console.error("Get user error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching user" }],
    });
  }
};

// Update user
exports.updateUser = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }

    const { firstName, lastName, email, role, tier } = req.body;
    const updateData = {};

    if (firstName) updateData.firstName = firstName;
    if (lastName) updateData.lastName = lastName;
    if (email) updateData.email = email;
    if (role) updateData.role = role;
    if (tier) updateData.tier = tier;

    const user = await User.findByIdAndUpdate(req.params.id, updateData, {
      new: true,
      runValidators: true,
    }).select(
      "-password -emailVerificationToken -passwordResetToken -passwordResetExpires"
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "User not found" }],
      });
    }

    res.json({
      success: true,
      data: user,
    });
  } catch (error) {
    console.error("Update user error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while updating user" }],
    });
  }
};

// Delete user
exports.deleteUser = async (req, res) => {
  try {
    const user = await User.findByIdAndDelete(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "User not found" }],
      });
    }

    res.json({
      success: true,
      data: { message: "User deleted successfully" },
    });
  } catch (error) {
    console.error("Delete user error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while deleting user" }],
    });
  }
};

// Get comprehensive user statistics
// exports.getUserStats = async (req, res) => {
//   try {
//     // Support both /me/stats and /:id/stats
//     const targetUserId = req.params.id || (req.user && req.user._id);

//     const user = await User.findById(targetUserId)
//       .select("stats questionAttempts")
//       .populate("questionAttempts.questionId", "subject")
//       .lean();

//     if (!user) {
//       return res.status(404).json({
//         success: false,
//         errors: [{ msg: "User not found" }],
//       });
//     }

//     const attempts = Array.isArray(user.questionAttempts)
//       ? user.questionAttempts
//       : [];

//     const totalAttempts = attempts.length;
//     const totalCorrect = attempts.filter((a) => !!a.isCorrect).length;
//     const accuracyPercentage =
//       totalAttempts > 0 ? Math.round((totalCorrect / totalAttempts) * 100) : 0;

//     // Questions answered today
//     const startOfToday = new Date();
//     startOfToday.setHours(0, 0, 0, 0);
//     const questionsAnsweredToday = attempts.filter(
//       (a) => a.attemptedAt && new Date(a.attemptedAt) >= startOfToday
//     ).length;

//     // Average time per question (consider only attempts with timeSpent > 0)
//     const timeAttempts = attempts.filter(
//       (a) => typeof a.timeSpent === "number" && a.timeSpent > 0
//     );
//     const averageTimePerQuestion =
//       timeAttempts.length > 0
//         ? Math.round(
//             timeAttempts.reduce((sum, a) => sum + a.timeSpent, 0) /
//               timeAttempts.length
//           )
//         : 0;

//     // Favorite subjects (most attempted) with correct counts
//     const subjectCounts = new Map();
//     const subjectCorrectCounts = new Map();
//     for (const a of attempts) {
//       if (a.subject) {
//         const key = a.subject.toString();
//         subjectCounts.set(key, (subjectCounts.get(key) || 0) + 1);
//         if (a.isCorrect) {
//           subjectCorrectCounts.set(
//             key,
//             (subjectCorrectCounts.get(key) || 0) + 1
//           );
//         }
//       }
//     }
//     const topSubjectIds = Array.from(subjectCounts.entries())
//       .sort((a, b) => b[1] - a[1])
//       .slice(0, 5)
//       .map(([id]) => id);

//     let favoriteSubjects = [];
//     if (topSubjectIds.length > 0) {
//       const subjects = await Subject.find({ _id: { $in: topSubjectIds } })
//         .select("name slug color")
//         .lean();
//       const subjectById = new Map(subjects.map((s) => [s._id.toString(), s]));
//       favoriteSubjects = topSubjectIds.map((id) => ({
//         id,
//         name: subjectById.get(id)?.name || "Unknown",
//         slug: subjectById.get(id)?.slug || null,
//         color: subjectById.get(id)?.color || null,
//         attempts: subjectCounts.get(id) || 0,
//         correct: subjectCorrectCounts.get(id) || 0,
//       }));
//     }

//     // Total questions in the system (active)
//     const totalQuestions = await Question.countDocuments({ isActive: true });

//     // Questions solved per subject (unique correctly answered question ids per subject)
//     // Build a map subjectId -> Set(questionId)
//     const solvedBySubject = new Map();
//     for (const a of attempts) {
//       if (!a.isCorrect) continue;

//       // Determine subject id: prefer attempt.subject, else try populated questionId.subject
//       let subjId = null;
//       if (a.subject) subjId = String(a.subject);
//       else if (a.questionId && a.questionId.subject)
//         subjId = String(a.questionId.subject);

//       if (!subjId) continue;

//       // Determine question id
//       let qid = null;
//       if (!a.questionId) continue;
//       if (typeof a.questionId === "string" || typeof a.questionId === "number")
//         qid = String(a.questionId);
//       else if (a.questionId._id) qid = String(a.questionId._id);
//       else qid = String(a.questionId);

//       if (!qid) continue;

//       if (!solvedBySubject.has(subjId)) solvedBySubject.set(subjId, new Set());
//       solvedBySubject.get(subjId).add(qid);
//     }

//     // Retrieve all subjects to include titles and totalQuestions per subject
//     // Sort subjects by number of solved questions (descending)
//     const allSubjects = await Subject.find()
//       .select("name slug color stats.totalQuestions")
//       .lean();

//     // Sort after fetching, since solved count is computed above
//     allSubjects.sort((a, b) => {
//       const aSolved = solvedBySubject.get(String(a._id))?.size || 0;
//       const bSolved = solvedBySubject.get(String(b._id))?.size || 0;
//       return bSolved - aSolved;
//     });
//     const questionsSolvedPerSubject = allSubjects.map((s) => {
//       const id = String(s._id);
//       return {
//         id,
//         name: s.name,
//         slug: s.slug || null,
//         color: s.color || null,
//         totalQuestions: s.stats?.totalQuestions || 0,
//         solved: solvedBySubject.get(id) ? solvedBySubject.get(id).size : 0,
//       };
//     });

//     // Recent activity summary (last 10 attempts)
//     const recentActivity = attempts
//       .slice() // copy
//       .sort((a, b) => new Date(b.attemptedAt) - new Date(a.attemptedAt))
//       .slice(0, 10)
//       .map((a) => ({
//         questionId: a.questionId,
//         subjectId: a.subject || null,
//         isCorrect: !!a.isCorrect,
//         timeSpent: typeof a.timeSpent === "number" ? a.timeSpent : 0,
//         attemptedAt: a.attemptedAt,
//       }));

//     const responseData = {
//       totalCorrectlyAnswered: totalCorrect,
//       totalQuestionAttempts: totalAttempts,
//       totalQuestions,
//       currentStreak: user.stats?.currentStreak || 0,
//       accuracyPercentage,
//       questionsAnsweredToday,
//       averageTimePerQuestion,
//       favoriteSubjects,
//       questionsSolvedPerSubject,
//       recentActivity,
//     };

//     return res.json({ success: true, data: responseData });
//   } catch (error) {
//     console.error("Get user stats error:", error);
//     return res.status(500).json({
//       success: false,
//       errors: [{ msg: "Server error while fetching user statistics" }],
//     });
//   }
// };

exports.getUserStats = async (req, res) => {
  try {
    const targetUserId = req.params.id || (req.user && req.user._id);

    // Aggregate to compute attempts summary and per-subject unique correct question counts
    const pipeline = [
      { $match: { _id: targetUserId } },
      { $project: { stats: 1, questionAttempts: 1 } },
      {
        $unwind: {
          path: "$questionAttempts",
          preserveNullAndEmptyArrays: true,
        },
      },

      // Lookup question data to get subject information
      {
        $lookup: {
          from: "questions", // Make sure this matches your collection name
          localField: "questionAttempts.questionId",
          foreignField: "_id",
          as: "questionData"
        }
      },

      // Normalize fields for easier grouping
      {
        $addFields: {
          "questionAttempts.qId": "$questionAttempts.questionId",
          "questionAttempts.subj": { 
            $arrayElemAt: ["$questionData.subject", 0] 
          },
        },
      },

      // Compute totals and flags
      {
        $group: {
          _id: "$_id",
          stats: { $first: "$stats" },
          attempts: { $push: "$questionAttempts" },
          totalAttempts: {
            $sum: { $cond: [{ $ifNull: ["$questionAttempts", false] }, 1, 0] },
          },
          totalCorrect: {
            $sum: {
              $cond: [{ $eq: ["$questionAttempts.isCorrect", true] }, 1, 0],
            },
          },
        },
      },

      // Prepare per-subject unique correct counts by mapping attempts -> {subj, qId}
      {
        $project: {
          stats: 1,
          attempts: 1,
          totalAttempts: 1,
          totalCorrect: 1,
          correctAttempts: {
            $filter: {
              input: "$attempts",
              as: "a",
              cond: { 
                $and: [
                  { $eq: ["$$a.isCorrect", true] },
                  { $ne: ["$$a.subj", null] } // Filter out attempts without subject
                ]
              },
            },
          },
        },
      },

      {
        $unwind: { path: "$correctAttempts", preserveNullAndEmptyArrays: true },
      },

      {
        $group: {
          _id: { user: "$_id", subj: "$correctAttempts.subj" },
          uniqueQuestions: { $addToSet: "$correctAttempts.qId" },
          totalAttempts: { $first: "$totalAttempts" },
          totalCorrect: { $first: "$totalCorrect" },
          stats: { $first: "$stats" },
        },
      },

      // Convert subject groups back to array
      {
        $group: {
          _id: "$_id.user",
          stats: { $first: "$stats" },
          totalAttempts: { $first: "$totalAttempts" },
          totalCorrect: { $first: "$totalCorrect" },
          perSubject: {
            $push: {
              $cond: {
                if: { $ne: ["$_id.subj", null] },
                then: {
                  subjectId: "$_id.subj",
                  solvedCount: { $size: "$uniqueQuestions" },
                },
                else: "$$REMOVE"
              }
            }
          },
        },
      },
    ];

    const aggRes = await User.aggregate(pipeline).allowDiskUse(true);
    if (!aggRes || !aggRes[0]) {
      return res
        .status(404)
        .json({ success: false, errors: [{ msg: "User not found" }] });
    }

    const agg = aggRes[0];

    console.log(agg)

    // Compute additional fields
    const totalAttempts = agg.totalAttempts || 0;
    const totalCorrect = agg.totalCorrect || 0;
    const accuracyPercentage =
      totalAttempts > 0 ? Math.round((totalCorrect / totalAttempts) * 100) : 0;

    // Questions answered today
    const startOfToday = new Date();
    startOfToday.setHours(0, 0, 0, 0);
    const questionsAnsweredToday = (agg.attempts || []).filter(
      (a) => a && a.attemptedAt && new Date(a.attemptedAt) >= startOfToday
    ).length;

    // Average time per question (consider only attempts with timeSpent > 0)
    const timeAttempts = (agg.attempts || []).filter(
      (a) => a && typeof a.timeSpent === "number" && a.timeSpent > 0
    );
    const averageTimePerQuestion =
      timeAttempts.length > 0
        ? Math.round(
            timeAttempts.reduce((s, a) => s + a.timeSpent, 0) /
              timeAttempts.length
          )
        : 0;

    // Favorite subjects: sort perSubject by solvedCount desc and take top 5
    const perSubjectMap = new Map();
    (agg.perSubject || []).forEach((p) => {
      if (!p.subjectId) return;
      perSubjectMap.set(String(p.subjectId), p.solvedCount || 0);
    });

    console.log(perSubjectMap)

    const topSubjectIds = Array.from(perSubjectMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([id]) => id);

    let favoriteSubjects = [];
    if (topSubjectIds.length > 0) {
      const subjects = await Subject.find({ _id: { $in: topSubjectIds } })
        .select("name slug color")
        .lean();
      const subjectById = new Map(subjects.map((s) => [String(s._id), s]));
      favoriteSubjects = topSubjectIds.map((id) => {
        const sid = String(id);
        return {
          id: sid,
          name: subjectById.get(sid)?.name || "Unknown",
          slug: subjectById.get(sid)?.slug || null,
          color: subjectById.get(sid)?.color || null,
          attempts: 0, // attempts per subject not tracked in this optimized pipeline
          correct: perSubjectMap.get(sid) || 0,
        };
      });
    }

    // Total questions in system (active)
    const totalQuestions = await Question.countDocuments({ isActive: true });

    // Fetch all subjects and merge solved counts (keep sort by solved desc)
    const allSubjects = await Subject.find()
      .select("name slug color stats.totalQuestions")
      .lean();
    const questionsSolvedPerSubject = allSubjects
      .map((s) => ({
        id: String(s._id),
        name: s.name,
        slug: s.slug || null,
        color: s.color || null,
        totalQuestions: s.stats?.totalQuestions || 0,
        solved: perSubjectMap.get(String(s._id)) || 0,
      }))
      .sort((a, b) => b.solved - a.solved);

    // Recent activity: reuse attempts array and sort
    const attemptsArr = (agg.attempts || [])
      .slice()
      .sort((a, b) => new Date(b.attemptedAt) - new Date(a.attemptedAt))
      .slice(0, 10)
      .map((a) => ({
        questionId: a.questionId,
        subjectId: a.subj || null, // Use the looked-up subject
        isCorrect: !!a.isCorrect,
        timeSpent: typeof a.timeSpent === "number" ? a.timeSpent : 0,
        attemptedAt: a.attemptedAt,
      }));

    const responseData = {
      totalCorrectlyAnswered: totalCorrect,
      totalQuestionAttempts: totalAttempts,
      totalQuestions,
      currentStreak: agg.stats?.currentStreak || 0,
      accuracyPercentage,
      questionsAnsweredToday,
      averageTimePerQuestion,
      favoriteSubjects,
      questionsSolvedPerSubject,
      recentActivity: attemptsArr,
    };

    return res.json({ success: true, data: responseData });
  } catch (error) {
    console.error("Get user stats error:", error);
    return res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching user statistics" }],
    });
  }
};

// Get user analytics (admin only)
exports.getUserAnalytics = async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select(
      "stats quizHistory testHistory dailyQuestions"
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "User not found" }],
      });
    }

    // Calculate analytics
    const analytics = {
      totalQuizzes: user.quizHistory.length,
      totalTests: user.testHistory.length,
      totalDailyQuestions: user.dailyQuestions.length,
      averageQuizScore: user.stats.averageQuizScore || 0,
      averageTestScore: user.stats.averageTestScore || 0,
      dailyQuestionAccuracy: user.stats.dailyQuestionAccuracy || 0,
      lastActive: user.stats.lastActive,
      subscriptionStatus: user.subscription.status,
      subscriptionPlan: user.subscription.plan,
    };

    res.json({
      success: true,
      data: analytics,
    });
  } catch (error) {
    console.error("Get user analytics error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching user analytics" }],
    });
  }
};

// Get user progress
exports.getUserProgress = async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select(
      "stats quizHistory testHistory dailyQuestions"
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "User not found" }],
      });
    }

    // Calculate progress
    const progress = {
      totalQuestionsAnswered: user.stats.questionsAnswered,
      totalCorrectAnswers: user.stats.correctAnswers,
      quizzesCompleted: user.stats.quizzesCompleted,
      testsCompleted: user.stats.testsCompleted,
      dailyQuestionsAnswered: user.stats.dailyQuestionsAnswered,
      dailyQuestionsCorrect: user.stats.dailyQuestionsCorrect,
      overallAccuracy: user.stats.overallAccuracy || 0,
      lastActive: user.stats.lastActive,
    };

    res.json({
      success: true,
      data: progress,
    });
  } catch (error) {
    console.error("Get user progress error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching user progress" }],
    });
  }
};

// Get user history
exports.getUserHistory = async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .select("quizHistory testHistory dailyQuestions")
      .populate("quizHistory.quiz", "title subject")
      .populate("testHistory.test", "title subject")
      .populate("dailyQuestions.question", "text subject");

    if (!user) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "User not found" }],
      });
    }

    res.json({
      success: true,
      data: {
        quizHistory: user.quizHistory,
        testHistory: user.testHistory,
        dailyQuestions: user.dailyQuestions,
      },
    });
  } catch (error) {
    console.error("Get user history error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching user history" }],
    });
  }
};

// Get user leaderboard
exports.getUserLeaderboard = async (req, res) => {
  try {
    const users = await User.find()
      .select("firstName lastName stats")
      .sort({ "stats.overallScore": -1 })
      .limit(10);

    res.json({
      success: true,
      data: users,
    });
  } catch (error) {
    console.error("Get leaderboard error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching leaderboard" }],
    });
  }
};

// Update user preferences
exports.updateUserPreferences = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }

    const { emailNotifications, pushNotifications, theme } = req.body;
    const updateData = {
      preferences: {
        ...req.user.preferences,
        emailNotifications,
        pushNotifications,
        theme,
      },
    };

    const user = await User.findByIdAndUpdate(req.user._id, updateData, {
      new: true,
    }).select(
      "-password -emailVerificationToken -passwordResetToken -passwordResetExpires"
    );

    res.json({
      success: true,
      data: user,
    });
  } catch (error) {
    console.error("Update preferences error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while updating preferences" }],
    });
  }
};

// Update user subscription
exports.updateUserSubscription = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }

    const { status, plan, startDate, endDate } = req.body;
    const updateData = {
      subscription: {
        status,
        plan,
        startDate,
        endDate,
      },
    };

    const user = await User.findByIdAndUpdate(req.user._id, updateData, {
      new: true,
    }).select(
      "-password -emailVerificationToken -passwordResetToken -passwordResetExpires"
    );

    res.json({
      success: true,
      data: user,
    });
  } catch (error) {
    console.error("Update subscription error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while updating subscription" }],
    });
  }
};

// Reset user password
exports.resetUserPassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Get user with password
    const user = await User.findById(req.user._id).select("+password");

    // Check current password
    const isMatch = await user.comparePassword(currentPassword);
    if (!isMatch) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Current password is incorrect" }],
      });
    }

    // Update password
    user.password = newPassword;
    await user.save();

    res.json({
      success: true,
      data: { message: "Password reset successfully" },
    });
  } catch (error) {
    console.error("Reset password error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while resetting password" }],
    });
  }
};

// Deactivate user account
exports.deactivateUser = async (req, res) => {
  try {
    const user = await User.findByIdAndUpdate(
      req.user._id,
      { isActive: false },
      { new: true }
    ).select(
      "-password -emailVerificationToken -passwordResetToken -passwordResetExpires"
    );

    res.json({
      success: true,
      data: { message: "Account deactivated successfully" },
    });
  } catch (error) {
    console.error("Deactivate account error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while deactivating account" }],
    });
  }
};

// Reactivate user account
exports.reactivateUser = async (req, res) => {
  try {
    const user = await User.findByIdAndUpdate(
      req.user._id,
      { isActive: true },
      { new: true }
    ).select(
      "-password -emailVerificationToken -passwordResetToken -passwordResetExpires"
    );

    res.json({
      success: true,
      data: { message: "Account reactivated successfully" },
    });
  } catch (error) {
    console.error("Reactivate account error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while reactivating account" }],
    });
  }
};

// Get user profile
exports.getProfile = async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select(
      "-password -emailVerificationToken -passwordResetToken -passwordResetExpires"
    );

    res.json({
      success: true,
      data: user,
    });
  } catch (error) {
    console.error("Get profile error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while fetching profile" }],
    });
  }
};

// Update user profile
exports.updateProfile = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }

    const { name, preferences } = req.body;
    const updateData = {};

    if (name) updateData.name = name;
    if (preferences) {
      updateData.preferences = {
        ...req.user.preferences,
        ...preferences,
      };
    }

    const user = await User.findByIdAndUpdate(req.user._id, updateData, {
      new: true,
      runValidators: true,
    }).select(
      "-password -emailVerificationToken -passwordResetToken -passwordResetExpires"
    );

    res.json({
      success: true,
      data: user,
    });
  } catch (error) {
    console.error("Update profile error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while updating profile" }],
    });
  }
};

// Change password
exports.changePassword = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }

    const { currentPassword, newPassword } = req.body;

    // Get user with password
    const user = await User.findById(req.user._id).select("+password");

    // Check current password
    const isMatch = await user.comparePassword(currentPassword);
    if (!isMatch) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Current password is incorrect" }],
      });
    }

    // Update password
    user.password = newPassword;
    await user.save();

    res.json({
      success: true,
      data: { message: "Password updated successfully" },
    });
  } catch (error) {
    console.error("Change password error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while changing password" }],
    });
  }
};

// Update user avatar
exports.updateAvatar = async (req, res) => {
  try {
    const { avatarUrl } = req.body;

    if (!avatarUrl) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Avatar URL is required" }],
      });
    }

    const user = await User.findByIdAndUpdate(
      req.user._id,
      { avatar: avatarUrl },
      { new: true }
    ).select(
      "-password -emailVerificationToken -passwordResetToken -passwordResetExpires"
    );

    res.json({
      success: true,
      data: user,
    });
  } catch (error) {
    console.error("Update avatar error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while updating avatar" }],
    });
  }
};

// Delete user account
exports.deleteAccount = async (req, res) => {
  try {
    const { password } = req.body;

    // Verify password
    const user = await User.findById(req.user._id).select("+password");
    const isMatch = await user.comparePassword(password);

    if (!isMatch) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Password is incorrect" }],
      });
    }

    // Delete user
    await User.findByIdAndDelete(req.user._id);

    res.json({
      success: true,
      data: { message: "Account deleted successfully" },
    });
  } catch (error) {
    console.error("Delete account error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Server error while deleting account" }],
    });
  }
};
