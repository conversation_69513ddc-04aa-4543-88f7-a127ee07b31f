import {
  AIHighlight,
  CharacterCount,
  Color,
  CustomKeymap,
  GlobalDragHandle,
  HighlightExtension,
  HorizontalRule,
  Mathematics,
  Placeholder,
  StarterKit,
  TaskItem,
  TaskList,
  TextStyle,
  TiptapImage,
  TiptapLink,
  TiptapUnderline,
  Twitter,
  UpdatedImage,
  UploadImagesPlugin,
  Youtube,
} from "novel";

import TextAlign from "@tiptap/extension-text-align";
import Table from "@tiptap/extension-table";
import TableRow from "@tiptap/extension-table-row";
import TableHeader from "@tiptap/extension-table-header";
import TableCell from "@tiptap/extension-table-cell";
import Details from "@tiptap/extension-details";
import DetailsSummary from "@tiptap/extension-details-summary";
import DetailsContent from "@tiptap/extension-details-content";

import { cx } from "class-variance-authority";
// import { common, createLowlight } from "lowlight";

//TODO I am using cx here to get tailwind autocomplete working, idk if someone else can write a regex to just capture the class key in objects
const aiHighlight = AIHighlight;
//You can overwrite the placeholder with your own configuration
const placeholder = Placeholder;
const tiptapLink = TiptapLink.configure({
  HTMLAttributes: {
    class: cx(
      "text-muted-foreground underline underline-offset-[3px] hover:text-primary transition-colors cursor-pointer",
    ),
  },
});

const tiptapImage = TiptapImage.extend({
  addProseMirrorPlugins() {
    return [
      UploadImagesPlugin({
        imageClass: cx("opacity-40 rounded-lg border border-stone-200"),
      }),
    ];
  },
}).configure({
  allowBase64: true,
  HTMLAttributes: {
    class: cx("rounded-lg border border-muted"),
  },
});

const updatedImage = UpdatedImage.configure({
  HTMLAttributes: {
    class: cx("rounded-lg border border-muted"),
  },
});

const taskList = TaskList.configure({
  HTMLAttributes: {
    class: cx("not-prose pl-2 "),
  },
});
const taskItem = TaskItem.configure({
  HTMLAttributes: {
    class: cx("flex gap-2 items-start my-4"),
  },
  nested: true,
});

const horizontalRule = HorizontalRule.configure({
  HTMLAttributes: {
    class: cx("mt-4 mb-6 border-t border-muted-foreground"),
  },
});

const starterKit = StarterKit.configure({
  bulletList: {
    HTMLAttributes: {
      class: cx("list-disc list-outside leading-3 -mt-2"),
    },
  },
  orderedList: {
    HTMLAttributes: {
      class: cx("list-decimal list-outside leading-3 -mt-2"),
    },
  },
  listItem: {
    HTMLAttributes: {
      class: cx("leading-normal -mb-2"),
    },
  },
  blockquote: {
    HTMLAttributes: {
      class: cx("border-l-4 border-primary"),
    },
  },
  codeBlock: {
    HTMLAttributes: {
      class: cx("rounded-md bg-muted text-muted-foreground border p-5 font-mono font-medium"),
    },
  },
  code: {
    HTMLAttributes: {
      class: cx("rounded-md bg-muted  px-1.5 py-1 font-mono font-medium"),
      spellcheck: "false",
    },
  },
  horizontalRule: false,
  dropcursor: {
    color: "#DBEAFE",
    width: 4,
  },
  gapcursor: false,
});


const youtube = Youtube.configure({
  HTMLAttributes: {
    class: cx(
      "rounded-lg border border-muted shadow-sm",
      "w-full max-w-full",
      "aspect-video",
      "my-4 mx-auto",
      "overflow-hidden",
      "bg-muted/50",
      "transition-all duration-200",
      "hover:shadow-md hover:border-muted-foreground/20"
    ),
  },
  inline: false,
  width: 640,
  height: 315,
  allowFullscreen: true,
  controls: true,
  nocookie: true,
});

const twitter = Twitter.configure({
  HTMLAttributes: {
    class: cx("not-prose"),
  },
  inline: false,
});

const mathematics = Mathematics.configure({
  HTMLAttributes: {
    class: cx("text-foreground rounded p-1 hover:bg-accent cursor-pointer"),
  },
  katexOptions: {
    throwOnError: false,
  },
});

const characterCount = CharacterCount.configure();

const textAlign = TextAlign.configure({
  types: ['heading', 'paragraph'],
  alignments: ['left', 'center', 'right', 'justify'],
  defaultAlignment: 'left',
});

const table = Table.configure({
  resizable: true,
  handleWidth: 5,
  cellMinWidth: 20,
  allowTableNodeSelection: true,
  HTMLAttributes: {
    class: cx("border-collapse table-auto w-full border border-muted"),
  },
});

const tableRow = TableRow.configure({
  HTMLAttributes: {
    class: cx("border-b border-muted"),
  },
});

const tableHeader = TableHeader.configure({
  HTMLAttributes: {
    class: cx("border border-muted bg-muted/50 px-4 py-2 text-left font-medium"),
  },
});

const tableCell = TableCell.configure({
  HTMLAttributes: {
    class: cx("border border-muted px-4 py-2"),
  },
});

const details = Details.configure({
  HTMLAttributes: {
    class: cx("my-4 rounded-lg border border-muted bg-muted/20"),
  },
});

const detailsSummary = DetailsSummary.configure({
  HTMLAttributes: {
    class: cx("cursor-pointer px-4 py-2 font-medium hover:bg-muted/50 rounded-t-lg"),
  },
});

const detailsContent = DetailsContent.configure({
  HTMLAttributes: {
    class: cx("px-4 py-2 border-t border-muted"),
  },
});

export const defaultExtensions = [
  starterKit,
  placeholder,
  tiptapLink,
  tiptapImage,
  updatedImage,
  taskList,
  taskItem,
  horizontalRule,
  aiHighlight,
  youtube,
  twitter,
  mathematics,
  characterCount,
  textAlign,
  table,
  tableRow,
  tableHeader,
  tableCell,
  details,
  detailsSummary,
  detailsContent,
  TiptapUnderline,
  HighlightExtension,
  TextStyle,
  Color,
  CustomKeymap,
  GlobalDragHandle,
];