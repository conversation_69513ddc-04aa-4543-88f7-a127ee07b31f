const { async<PERSON><PERSON><PERSON> } = require("../middleware/errorHandler");
const Question = require("../models/Question");

// POST /api/tests/comprehensive/start
// Body: { count: number, duration: number (minutes), difficulty?: 'easy'|'medium'|'hard'|'mixed' }
// Security: Do NOT send correct answers during the test
const startComprehensiveTest = asyncHandler(async (req, res) => {
  const { count = 100, duration = 180, difficulty = "mixed" } = req.body || {};

  const sampleSize = Math.max(1, Math.min(parseInt(count, 10) || 100, 200));

  // Build match filter
  const match = { isActive: true };
  if (difficulty && difficulty !== "mixed") {
    match.difficulty = difficulty;
  }
  // Apply tier filter based on user premium access
  if (
    req.user &&
    typeof req.user.isPremiumActive === "function" &&
    !req.user.isPremiumActive()
  ) {
    match.tier = "free";
  }

  // Aggregate random questions, excluding sensitive fields
  const questions = await Question.aggregate([
    { $match: match },
    { $sample: { size: sampleSize } },
    // {
    //   $lookup: {
    //     from: "topics",
    //     localField: "topic",
    //     foreignField: "_id",
    //     as: "topic",
    //   },
    // },
    {
      $project: {
        text: 1,
        topic: 1,
        subject: 1,
        difficulty: 1,
        tier: 1,
        options: {
          $map: {
            input: "$options",
            as: "opt",
            in: { text: "$$opt.text" }, // strip isCorrect
          },
        },
      },
    },
  ]);

  // console.log(JSON.stringify(questions[0], null, 2));

  return res.status(200).json({
    success: true,
    data: {
      sessionId: `${Date.now()}_${req.user?._id || "anon"}`,
      totalQuestions: questions.length,
      questions,
      timeLimit: Math.max(60, (parseInt(duration, 10) || 180) * 60), // seconds
      createdAt: new Date().toISOString(),
      mode: "comprehensive-test",
    },
  });
});

// POST /api/tests/comprehensive/submit
// Body: { sessionId: string, answers: Array<{ questionId: string, selectedAnswer: number }>, timeSpent?: number }
const submitComprehensiveTest = asyncHandler(async (req, res) => {
  const { sessionId, answers = [], timeSpent = 0 } = req.body || {};

  if (!Array.isArray(answers) || answers.length === 0) {
    return res.status(400).json({
      success: false,
      errors: [{ msg: "Answers array is required" }],
    });
  }

  const ids = answers.map((a) => a.questionId).filter(Boolean);
  const questions = await Question.find({ _id: { $in: ids } }).lean();

  // Index questions by id for quick lookup
  const byId = new Map(questions.map((q) => [String(q._id), q]));

  let correctCount = 0;
  const review = answers.map((ans) => {
    const q = byId.get(String(ans.questionId));
    if (!q) return { questionId: ans.questionId, isCorrect: false };
    const isCorrect = !!(
      q.options &&
      q.options[ans.selectedAnswer] &&
      q.options[ans.selectedAnswer].isCorrect === true
    );
    if (isCorrect) correctCount += 1;
    return {
      questionId: String(q._id),
      isCorrect,
      selectedAnswer: ans.selectedAnswer,
      correctAnswer: q.options.findIndex((o) => o.isCorrect),
      question: {
        _id: String(q._id),
        text: q.text,
        options: q.options, // include isCorrect now for review
        explanationHtml: q.explanationHtml || "",
        topic: q.topic,
        subject: q.subject,
        difficulty: q.difficulty,
        tier: q.tier,
      },
    };
  });

  const totalQuestions = answers.length;
  const score = Math.round((correctCount / Math.max(1, totalQuestions)) * 100);

  return res.status(200).json({
    success: true,
    data: {
      sessionId,
      score,
      correct: correctCount,
      totalQuestions,
      percentage: score,
      timeSpent,
      review,
      completedAt: new Date().toISOString(),
    },
    message: "Test completed successfully",
  });
});

module.exports = { startComprehensiveTest, submitComprehensiveTest };
