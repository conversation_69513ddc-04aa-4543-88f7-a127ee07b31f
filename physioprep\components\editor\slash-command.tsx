import {
  CheckSquare,
  Code,
  Heading1,
  Heading2,
  Heading3,
  ImageIcon,
  List,
  ListOrdered,
  MessageSquarePlus,
  Text,
  TextQuote,
  Youtube,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Table,
  ChevronDown,
  TableRowsSplit,
  TableColumnsSplitIcon,
} from "lucide-react";
import { Command, createSuggestionItems, renderItems } from "novel";
import { uploadFn } from "./image-upload";

export const suggestionItems = createSuggestionItems([
  {
    title: "Send Feedback",
    description: "Let us know how we can improve.",
    icon: <MessageSquarePlus size={18} />,
    command: ({ editor, range }) => {
      console.log("Feedback command executed");
      editor.chain().focus().deleteRange(range).run();
      window.open("/feedback", "_blank");
    },
  },
  {
    title: "Text",
    description: "Just start typing with plain text.",
    searchTerms: ["p", "paragraph"],
    icon: <Text size={18} />,
    command: ({ editor, range }) => {
      console.log("Text command executed, range:", range);
      console.log("Available commands:", Object.keys(editor.commands));
      
      const result = editor
        .chain()
        .focus()
        .deleteRange(range)
        .setParagraph()
        .run();
      
      console.log("setParagraph result:", result);
      return result;
    },
  },
  {
    title: "To-do List",
    description: "Track tasks with a to-do list.",
    searchTerms: ["todo", "task", "list", "check", "checkbox"],
    icon: <CheckSquare size={18} />,
    command: ({ editor, range }) => {
      console.log("Todo command executed");
      return editor.chain().focus().deleteRange(range).toggleTaskList().run();
    },
  },
  {
    title: "Heading 1",
    description: "Big section heading.",
    searchTerms: ["title", "big", "large", "h1"],
    icon: <Heading1 size={18} />,
    command: ({ editor, range }) => {
      console.log("Heading 1 command executed");
      console.log("Available commands:", Object.keys(editor.commands));
      console.log("Can toggle heading:", editor.can().toggleHeading({ level: 1 }));
      console.log("Can set heading:", editor.can().setHeading({ level: 1 }));
      console.log("Current node:", editor.state.selection.$anchor.parent.type.name);
      
      // Try multiple approaches
      let result = false;
      
      // Method 1: toggleHeading
      result = editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleHeading({ level: 1 })
        .run();
      
      console.log("toggleHeading result:", result);
      
      // If toggleHeading fails, try setHeading
      if (!result) {
        console.log("toggleHeading failed, trying setHeading");
        result = editor
          .chain()
          .focus()
          .deleteRange(range)
          .setHeading({ level: 1 })
          .run();
        console.log("setHeading result:", result);
      }
      
      // If both fail, try direct node setting
      if (!result) {
        console.log("Both methods failed, trying setNode");
        result = editor
          .chain()
          .focus()
          .deleteRange(range)
          .setNode('heading', { level: 1 })
          .run();
        console.log("setNode result:", result);
      }
      
      return result;
    },
  },
  {
    title: "Heading 2",
    description: "Medium section heading.",
    searchTerms: ["subtitle", "medium", "h2"],
    icon: <Heading2 size={18} />,
    command: ({ editor, range }) => {
      console.log("Heading 2 command executed");
      console.log("Can toggle heading:", editor.can().toggleHeading({ level: 2 }));
      
      let result = editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleHeading({ level: 2 })
        .run();
      
      if (!result) {
        result = editor
          .chain()
          .focus()
          .deleteRange(range)
          .setHeading({ level: 2 })
          .run();
      }
      
      if (!result) {
        result = editor
          .chain()
          .focus()
          .deleteRange(range)
          .setNode('heading', { level: 2 })
          .run();
      }
      
      console.log("Heading 2 result:", result);
      return result;
    },
  },
  {
    title: "Heading 3",
    description: "Small section heading.",
    searchTerms: ["subtitle", "small", "h3"],
    icon: <Heading3 size={18} />,
    command: ({ editor, range }) => {
      console.log("Heading 3 command executed");
      console.log("Can toggle heading:", editor.can().toggleHeading({ level: 3 }));
      
      let result = editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleHeading({ level: 3 })
        .run();
      
      if (!result) {
        result = editor
          .chain()
          .focus()
          .deleteRange(range)
          .setHeading({ level: 3 })
          .run();
      }
      
      if (!result) {
        result = editor
          .chain()
          .focus()
          .deleteRange(range)
          .setNode('heading', { level: 3 })
          .run();
      }
      
      console.log("Heading 3 result:", result);
      return result;
    },
  },
  {
    title: "Bullet List",
    description: "Create a simple bullet list.",
    searchTerms: ["unordered", "point"],
    icon: <List size={18} />,
    command: ({ editor, range }) => {
      console.log("Bullet list command executed");
      return editor.chain().focus().deleteRange(range).toggleBulletList().run();
    },
  },
  {
    title: "Numbered List",
    description: "Create a list with numbering.",
    searchTerms: ["ordered"],
    icon: <ListOrdered size={18} />,
    command: ({ editor, range }) => {
      console.log("Numbered list command executed");
      return editor.chain().focus().deleteRange(range).toggleOrderedList().run();
    },
  },
  {
    title: "Quote",
    description: "Capture a quote.",
    searchTerms: ["blockquote"],
    icon: <TextQuote size={18} />,
    command: ({ editor, range }) => {
      console.log("Quote command executed");
      return editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleBlockquote()
        .run();
    },
  },
  {
    title: "Code",
    description: "Capture a code snippet.",
    searchTerms: ["codeblock"],
    icon: <Code size={18} />,
    command: ({ editor, range }) => {
      console.log("Code command executed");
      return editor.chain().focus().deleteRange(range).toggleCodeBlock().run();
    },
  },
  {
    title: "Image",
    description: "Upload an image from your computer.",
    searchTerms: ["photo", "picture", "media"],
    icon: <ImageIcon size={18} />,
    command: ({ editor, range }) => {
      console.log("Image command executed");
      editor.chain().focus().deleteRange(range).run();
      const input = document.createElement("input");
      input.type = "file";
      input.accept = "image/*";
      input.onchange = async () => {
        if (input.files?.length) {
          const file = input.files[0];
          const pos = editor.view.state.selection.from;
          uploadFn(file, editor.view, pos);
        }
      };
      input.click();
    },
  },
  {
    title: "Youtube",
    description: "Embed a Youtube video.",
    searchTerms: ["video", "youtube", "embed"],
    icon: <Youtube size={18} />,
    command: ({ editor, range }) => {
      console.log("Youtube command executed");
      const videoLink = prompt("Please enter Youtube Video Link");
      const ytregex = new RegExp(
        /^((?:https?:)?\/\/)?((?:www|m)\.)?((?:youtube\.com|youtu.be))(\/(?:[\w\-]+\?v=|embed\/|v\/)?)([\w\-]+)(\S+)?$/
      );

      if (ytregex.test(videoLink as string)) {
        return editor
          .chain()
          .focus()
          .deleteRange(range)
          .setYoutubeVideo({
            src: videoLink as string,
          })
          .run();
      } else {
        if (videoLink !== null) {
          alert("Please enter a correct Youtube Video Link");
        }
      }
    },
  },
  {
    title: "Align Left",
    description: "Align text to the left.",
    searchTerms: ["align", "left", "alignment"],
    icon: <AlignLeft size={18} />,
    command: ({ editor, range }) => {
      console.log("Align Left command executed");
      return editor
        .chain()
        .focus()
        .deleteRange(range)
        .setTextAlign('left')
        .run();
    },
  },
  {
    title: "Align Center",
    description: "Center align text.",
    searchTerms: ["align", "center", "alignment"],
    icon: <AlignCenter size={18} />,
    command: ({ editor, range }) => {
      console.log("Align Center command executed");
      return editor
        .chain()
        .focus()
        .deleteRange(range)
        .setTextAlign('center')
        .run();
    },
  },
  {
    title: "Align Right",
    description: "Align text to the right.",
    searchTerms: ["align", "right", "alignment"],
    icon: <AlignRight size={18} />,
    command: ({ editor, range }) => {
      console.log("Align Right command executed");
      return editor
        .chain()
        .focus()
        .deleteRange(range)
        .setTextAlign('right')
        .run();
    },
  },
  {
    title: "Align Justify",
    description: "Justify text alignment.",
    searchTerms: ["align", "justify", "alignment"],
    icon: <AlignJustify size={18} />,
    command: ({ editor, range }) => {
      console.log("Align Justify command executed");
      return editor
        .chain()
        .focus()
        .deleteRange(range)
        .setTextAlign('justify')
        .run();
    },
  },
  {
    title: "Table",
    description: "Insert a table with 3 columns and 3 rows.",
    searchTerms: ["table", "grid", "data"],
    icon: <Table size={18} />,
    command: ({ editor, range }) => {
      console.log("Table command executed");
      return editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
        .run();
    },
  },
  {
    title: "Table (No Header)",
    description: "Insert a table with 3 columns and 3 rows without a header row.",
    searchTerms: ["table", "grid", "data", "no header"],
    icon: <Table size={18} />,
    command: ({ editor, range }) => {
      console.log("Table (No Header) command executed");
      return editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertTable({ rows: 3, cols: 3, withHeaderRow: false })
        .run();
    },
  },
  {
    title:"Table Row",
    description: "Insert a new row in the table.",
    searchTerms: ["table", "row", "insert"],
    icon: <TableRowsSplit size={18} />,
    command: ({ editor, range }) => {
      console.log("Table Row command executed");
      return editor
        .chain()
        .focus()
        .deleteRange(range)
        .addRowAfter()
        .run();
    },
  },
  {
    title: "Table Column",
    description: "Insert a new column in the table.",
    searchTerms: ["table", "column", "insert"],
    icon: <TableColumnsSplitIcon size={18} />,
    command: ({ editor, range }) => {
      console.log("Table Column command executed");
      return editor
        .chain()
        .focus()
        .deleteRange(range)
        .addColumnAfter()
        .run();
    },
  },
  {
    title: "Details",
    description: "Insert a collapsible details section.",
    searchTerms: ["details", "collapsible", "accordion", "expand"],
    icon: <ChevronDown size={18} />,
    command: ({ editor, range }) => {
      console.log("Details command executed");
      return editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertContent({
          type: 'details',
          content: [
            {
              type: 'detailsSummary',
              content: [{ type: 'text', text: 'Click to expand' }]
            },
            {
              type: 'detailsContent',
              content: [
                {
                  type: 'paragraph',
                  content: [{ type: 'text', text: 'Add your content here...' }]
                }
              ]
            }
          ]
        })
        .run();
    },
  },
]);

export const slashCommand = Command.configure({
  suggestion: {
    items: () => suggestionItems,
    render: renderItems,
  },
});