"use client";

import React, { useState } from "react";
import { JSONContent } from "novel";
import NovelEditor from "@/components/editor/NovelEditor";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const TestEditorPage = () => {
  const [editorData, setEditorData] = useState<JSONContent>({
    type: "doc",
    content: [
      {
        type: "heading",
        attrs: { level: 1 },
        content: [{ type: "text", text: "Welcome to the Novel Editor Test" }],
      },
      {
        type: "paragraph",
        content: [
          {
            type: "text",
            text: "This is a test page to verify that the Novel editor is working correctly with all features including headings.",
          },
        ],
      },
      {
        type: "heading",
        attrs: { level: 2 },
        content: [{ type: "text", text: "Features to Test" }],
      },
      {
        type: "bulletList",
        content: [
          {
            type: "listItem",
            content: [
              {
                type: "paragraph",
                content: [{ type: "text", text: "Heading 1, 2, 3 (using slash commands or node selector)" }],
              },
            ],
          },
          {
            type: "listItem",
            content: [
              {
                type: "paragraph",
                content: [{ type: "text", text: "Bold, italic, underline formatting" }],
              },
            ],
          },
          {
            type: "listItem",
            content: [
              {
                type: "paragraph",
                content: [{ type: "text", text: "Bullet and numbered lists" }],
              },
            ],
          },
          {
            type: "listItem",
            content: [
              {
                type: "paragraph",
                content: [{ type: "text", text: "Code blocks and inline code" }],
              },
            ],
          },
          {
            type: "listItem",
            content: [
              {
                type: "paragraph",
                content: [{ type: "text", text: "Task lists (to-do items)" }],
              },
            ],
          },
        ],
      },
    ],
  });

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Novel Editor Test Page</h1>
        <Badge variant="secondary">Test Environment</Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Editor</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border rounded-md min-h-[400px]">
              <NovelEditor data={editorData} setData={setEditorData} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Keyboard Shortcuts</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Headings</h4>
              <ul className="text-sm space-y-1">
                <li><code>Cmd/Ctrl + Alt + 1</code> - Heading 1</li>
                <li><code>Cmd/Ctrl + Alt + 2</code> - Heading 2</li>
                <li><code>Cmd/Ctrl + Alt + 3</code> - Heading 3</li>
                <li><code>Cmd/Ctrl + Alt + 0</code> - Paragraph</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Lists</h4>
              <ul className="text-sm space-y-1">
                <li><code>Cmd/Ctrl + Shift + 8</code> - Bullet List</li>
                <li><code>Cmd/Ctrl + Shift + 7</code> - Numbered List</li>
                <li><code>Cmd/Ctrl + Shift + X</code> - Task List</li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-2">Other</h4>
              <ul className="text-sm space-y-1">
                <li><code>Cmd/Ctrl + Shift + .</code> - Blockquote</li>
                <li><code>/</code> - Slash commands</li>
                <li>Select text for formatting bubble</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>JSON Output</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="bg-muted p-4 rounded-md text-sm overflow-auto max-h-96">
            {JSON.stringify(editorData, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
};

export default TestEditorPage;
