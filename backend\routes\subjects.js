const express = require('express');
const router = express.Router();
const { body, param } = require('express-validator');

// Import controllers
const {
  getSubjects,
  getSubject,
  createSubject,
  updateSubject,
  deleteSubject,
  getSubjectStats,
  getSubjectAnalytics,
  getSubjectQuestions,
  getSubjectQuizzes,
  getSubjectTests,
  getSubjectLeaderboard,
  getSubjectProgress,
  getSubjectCategories,
  addSubjectCategory,
  updateSubjectCategory,
  deleteSubjectCategory
} = require('../controllers/subjectController');

const topicController = require('../controllers/topicController');

// Import middleware
const { auth, admin, optionalAuth } = require('../middleware/auth');
const { handleValidationErrors } = require('../validators/authValidators');

// Validation for subject creation/update
const validateSubject = [
  body('name')
    .trim()
    .notEmpty()
    .withMessage('Subject name is required')
    .isLength({ max: 100 })
    .withMessage('Subject name cannot exceed 100 characters'),
  
  body('description')
    .trim()
    .notEmpty()
    .withMessage('Description is required'),
  
  body('icon')
    .optional()
    .trim(),
  
  body('color')
    .optional()
    .trim()
    .matches(/^#[0-9A-Fa-f]{6}$/)
    .withMessage('Color must be a valid hex code'),
  
  body('order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Order must be a non-negative integer'),
  
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  
  handleValidationErrors
];

// Validation for category operations
const validateCategory = [
  body('name')
    .trim()
    .notEmpty()
    .withMessage('Category name is required'),
  
  body('description')
    .optional()
    .trim(),
  
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  
  handleValidationErrors
];

// Public routes (with optional auth to detect admin users)
// @route   GET /api/subjects
// @desc    Get subjects
// @access  private
router.get('/', auth, getSubjects);

// @route   GET /api/subjects/:id
// @desc    Get subject by ID
// @access  private
router.get('/:id', auth ,param('id').isMongoId(), handleValidationErrors, getSubject);

// @route   GET /api/subjects/:id/stats
// @desc    Get subject stats
// @access  public
router.get('/:id/stats', param('id').isMongoId(), handleValidationErrors, getSubjectStats);

// @route   GET /api/subjects/:id/questions
// @desc    Get subject questions
// @access  public
router.get('/:id/questions', param('id').isMongoId(), handleValidationErrors, getSubjectQuestions);

// @route   GET /api/subjects/:id/quizzes
// @desc    Get subject quizzes
// @access  public
router.get('/:id/quizzes', param('id').isMongoId(), handleValidationErrors, getSubjectQuizzes);

// @route   GET /api/subjects/:id/tests
// @desc    Get subject tests
// @access  public
router.get('/:id/tests', param('id').isMongoId(), handleValidationErrors, getSubjectTests);

// @route   GET /api/subjects/:id/leaderboard
// @desc    Get subject leaderboard
// @access  public
router.get('/:id/leaderboard', param('id').isMongoId(), handleValidationErrors, getSubjectLeaderboard);

// @route   GET /api/subjects/:id/progress
// @desc    Get subject progress
// @access  private
router.get('/:id/progress', auth, param('id').isMongoId(), handleValidationErrors, getSubjectProgress);

// @route   GET /api/subjects/:id/categories
// @desc    Get subject categories
router.get('/:id/categories', param('id').isMongoId(), handleValidationErrors, getSubjectCategories);

// Topic routes
router.get('/topics', topicController.getTopics);
router.get('/topics/:id', topicController.getTopic);
router.post('/topics', [
  body('topicName').notEmpty().withMessage('Topic name is required'),
  body('subject').notEmpty().withMessage('Subject is required')
], topicController.createTopic);
router.put('/topics/:id', [
  body('topicName').optional().notEmpty().withMessage('Topic name cannot be empty'),
  body('subject').optional().notEmpty().withMessage('Subject is required')
], topicController.updateTopic);
router.delete('/topics/:id', topicController.deleteTopic);

// Admin routes
router.post('/', auth, admin, validateSubject, createSubject);
router.put('/:id', auth, admin, param('id').isMongoId(), validateSubject, handleValidationErrors, updateSubject);
router.delete('/:id', auth, admin, param('id').isMongoId(), handleValidationErrors, deleteSubject);
router.get('/:id/analytics', auth, admin, param('id').isMongoId(), handleValidationErrors, getSubjectAnalytics);
router.post('/:id/categories', auth, admin, param('id').isMongoId(), validateCategory, handleValidationErrors, addSubjectCategory);
router.put('/:id/categories/:categoryId', auth, admin, param('id').isMongoId(), param('categoryId').isMongoId(), validateCategory, handleValidationErrors, updateSubjectCategory);
router.delete('/:id/categories/:categoryId', auth, admin, param('id').isMongoId(), param('categoryId').isMongoId(), handleValidationErrors, deleteSubjectCategory);

module.exports = router;
