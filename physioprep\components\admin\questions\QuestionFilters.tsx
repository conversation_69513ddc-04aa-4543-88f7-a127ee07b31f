"use client";

import { useState, useEffect } from "react";
import { Filter,X, Check, RotateCcw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetFooter,
} from "@/components/ui/sheet";

import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Topic } from "@/types/types";
import { useAtom, useSetAtom } from "jotai";
import { fetchSubjectsForDropdownAtom, SubjectsAtom } from "@/store/Subject";
import { fetchTopicsForDropdown<PERSON>tom, topics<PERSON>tom } from "@/store/topics";

// Local filter state type (non-optional strings)
type LocalFilters = {
  search: string;
  subject: string;
  topic: string;
  difficulty: string;
  tier: string;
  isActive: string;
};

interface QuestionFiltersProps {
  filters: LocalFilters;
  onFilterChange: (filters: LocalFilters) => void;
}

export function QuestionFilters({
  filters: initialFilters,
  onFilterChange,
}: QuestionFiltersProps) {
  const [filters, setFilters] = useState(initialFilters);
  const [tempFilters, setTempFilters] = useState(initialFilters); // Temporary filters for apply button
  const [filteredTopics, setFilteredTopics] = useState<Topic[]>();
  const [isOpen, setIsOpen] = useState(false);

  const fetchsubjects = useSetAtom(fetchSubjectsForDropdownAtom);
  const fetchtopics = useSetAtom(fetchTopicsForDropdownAtom);
  const [subjects] = useAtom(SubjectsAtom);
  const [topics] = useAtom(topicsAtom);

  useEffect(() => {
    if (subjects.length === 0) {
      fetchsubjects();
    }
    if (topics.length === 0) {
      fetchtopics();
    }
  }, [fetchsubjects, fetchtopics, subjects.length, topics.length]);

  // Update local filters when props change
  useEffect(() => {
    setFilters(initialFilters);
    setTempFilters(initialFilters);
  }, [initialFilters]);

  // Filter topics based on selected subject
  useEffect(() => {
    if (tempFilters.subject) {
      console.log(tempFilters.subject);
      setFilteredTopics(
        topics.filter((topic) => {
          const topicSubjectId =
            typeof topic.subject === "object"
              ? topic.subject._id
              : topic.subject;
          return topicSubjectId === tempFilters.subject;
        })
      );
    } else {
      setFilteredTopics(topics);
    }
  }, [tempFilters.subject, topics]);

  const handleTempFilterChange = (key: string, value: string) => {
    const newTempFilters = {
      ...tempFilters,
      [key]: value === "all" ? "" : value,
    };

    // Clear topic filter when subject changes
    if (key === "subject" && value !== tempFilters.subject) {
      newTempFilters.topic = "";
    }

    setTempFilters(newTempFilters);
  };

  const applyFilters = () => {
    setFilters(tempFilters);
    onFilterChange(tempFilters);
    setIsOpen(false);
  };

  const clearFilters = () => {
    const clearedFilters = {
      search: "",
      subject: "",
      topic: "",
      difficulty: "",
      tier: "",
      isActive: "",
    };
    setTempFilters(clearedFilters);
    setFilters(clearedFilters);
    onFilterChange(clearedFilters);
  };

  const resetTempFilters = () => {
    setTempFilters(filters);
  };

  const hasActiveFilters = Object.values(filters).some((value) => value !== "");
  const hasChanges = JSON.stringify(filters) !== JSON.stringify(tempFilters);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}  >
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <Filter className="mr-2 h-4 w-4" />
          Filters
          {hasActiveFilters && (
            <Badge variant="secondary" className="ml-2 h-5 px-1.5 text-xs">
              {Object.values(filters).filter((value) => value !== "").length}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px]" >
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter Questions
          </SheetTitle>
          <SheetDescription>
            Configure filters to find specific questions. Changes will be
            applied when you click &quot;Apply Filters &quot;.
          </SheetDescription>
        </SheetHeader>

        <div className="space-y-6 mt-6 px-6">
          {/* Active Filters Display */}
          {hasActiveFilters && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Active Filters</Label>
              <div className="flex flex-wrap gap-2">
                {Object.entries(filters).map(([key, value]) => {
                  if (!value) return null;
                  let displayValue = value;
                  if (key === "subject") {
                    const subject = subjects.find((s) => s._id === value);
                    displayValue = subject?.name || value;
                  } else if (key === "topic") {
                    const topic = topics.find((t) => t._id === value);
                    displayValue = topic?.topicName || value;
                  } else if (key === "isActive") {
                    displayValue = value === "true" ? "Active" : "Inactive";
                  }
                  return (
                    <Badge key={key} variant="secondary" className="capitalize">
                      {key}: {displayValue}
                    </Badge>
                  );
                })}
              </div>
              <Separator />
            </div>
          )}

          {/* Subject Filter */}
          <div className="space-y-3">
            <Label htmlFor="subject-filter" className="text-sm font-medium">
              Subject
            </Label>
            <Select
              value={tempFilters.subject || "all"}
              onValueChange={(value) =>
                handleTempFilterChange("subject", value)
              }
            >
              <SelectTrigger id="subject-filter" className="h-10 w-full">
                <SelectValue placeholder="Select a subject..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All subjects</SelectItem>
                {subjects.map((subject) => (
                  <SelectItem key={subject._id} value={subject._id}>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: subject.color || "#6b7280" }}
                      />
                      {subject.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Topic Filter */}
          <div className="space-y-3">
            <Label htmlFor="topic-filter" className="text-sm font-medium">
              Topic
            </Label>
            <Select
              value={tempFilters.topic || "all"}
              onValueChange={(value) => handleTempFilterChange("topic", value)}
              disabled={!tempFilters.subject}
            >
              <SelectTrigger id="topic-filter" className="h-10 w-full">
                <SelectValue
                  placeholder={
                    tempFilters.subject
                      ? "Select a topic..."
                      : "Select subject first"
                  }
                />
              </SelectTrigger>
              <SelectContent className="">
                <SelectItem value="all">All topics</SelectItem>
                {filteredTopics?.map((topic) => (
                  <SelectItem key={topic._id} value={topic._id}>
                    <div className="flex items-center gap-2">
                      {topic.isPremium && (
                        <Badge variant="outline" className="text-xs px-1">
                          Premium
                        </Badge>
                      )}
                      {topic.topicName}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Difficulty Filter - Using Radio Group */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Difficulty Level</Label>
            <RadioGroup
              value={tempFilters.difficulty || "all"}
              onValueChange={(value) =>
                handleTempFilterChange("difficulty", value)
              }
              className="flex flex-wrap gap-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="all" id="diff-all" />
                <Label htmlFor="diff-all" className="text-sm">
                  All
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="easy" id="diff-easy" />
                <Label htmlFor="diff-easy" className="text-sm text-green-600">
                  Easy
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="medium" id="diff-medium" />
                <Label
                  htmlFor="diff-medium"
                  className="text-sm text-yellow-600"
                >
                  Medium
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="hard" id="diff-hard" />
                <Label htmlFor="diff-hard" className="text-sm text-red-600">
                  Hard
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Tier Filter - Using Checkboxes */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Access Tier</Label>
            <div className="flex gap-6">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="tier-all"
                  checked={
                    tempFilters.tier === "" || tempFilters.tier === "all"
                  }
                  onCheckedChange={(checked) => {
                    if (checked) handleTempFilterChange("tier", "all");
                  }}
                />
                <Label htmlFor="tier-all" className="text-sm">
                  All Tiers
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="tier-free"
                  checked={tempFilters.tier === "free"}
                  onCheckedChange={(checked) => {
                    handleTempFilterChange("tier", checked ? "free" : "all");
                  }}
                />
                <Label htmlFor="tier-free" className="text-sm text-blue-600">
                  Free
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="tier-premium"
                  checked={tempFilters.tier === "premium"}
                  onCheckedChange={(checked) => {
                    handleTempFilterChange("tier", checked ? "premium" : "all");
                  }}
                />
                <Label
                  htmlFor="tier-premium"
                  className="text-sm text-purple-600"
                >
                  Premium
                </Label>
              </div>
            </div>
          </div>

          {/* Status Filter - Using Toggle Buttons */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Status</Label>
            <div className="flex gap-2">
              <Button
                type="button"
                variant={
                  tempFilters.isActive === "" || tempFilters.isActive === "all"
                    ? "default"
                    : "outline"
                }
                size="sm"
                onClick={() => handleTempFilterChange("isActive", "all")}
                className="flex-1"
              >
                All
              </Button>
              <Button
                type="button"
                variant={
                  tempFilters.isActive === "true" ? "default" : "outline"
                }
                size="sm"
                onClick={() => handleTempFilterChange("isActive", "true")}
                className="flex-1"
              >
                Active
              </Button>
              <Button
                type="button"
                variant={
                  tempFilters.isActive === "false" ? "default" : "outline"
                }
                size="sm"
                onClick={() => handleTempFilterChange("isActive", "false")}
                className="flex-1"
              >
                Inactive
              </Button>
            </div>
          </div>
        </div>

        {/* Sheet Footer with Action Buttons */}
        <SheetFooter className="flex-col sm:flex-col gap-2 mt-6">
          <div className="flex gap-2 w-full">
            <Button
              type="button"
              variant="outline"
              onClick={resetTempFilters}
              disabled={!hasChanges}
              className="flex-1"
            >
              <X className="mr-2 h-4 w-4" />
              Reset
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={clearFilters}
              disabled={!hasActiveFilters}
              className="flex-1"
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Clear All
            </Button>
          </div>
          <Button
            type="button"
            onClick={applyFilters}
            disabled={!hasChanges}
            className="w-full mt-2"
          >
            <Check className="mr-2 h-4 w-4" />
            Apply Filters
            {hasChanges && (
              <Badge variant="secondary" className="ml-2 h-5 px-1.5 text-xs">
                {
                  Object.values(tempFilters).filter((value) => value !== "")
                    .length
                }
              </Badge>
            )}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
