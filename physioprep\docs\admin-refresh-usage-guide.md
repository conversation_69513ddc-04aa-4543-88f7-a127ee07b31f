# Admin Refresh Token Usage Guide

This guide explains how to use the admin refresh token functionality in the PhysioPrep application.

## Overview

The admin refresh system provides automatic token renewal to maintain admin sessions without requiring frequent re-authentication. It includes:

- **Backend API**: Secure token refresh endpoint
- **Frontend Utilities**: Server actions and React hooks
- **Automatic Refresh**: Background token renewal
- **Error Handling**: Graceful fallback to login

## Backend Implementation

### API Endpoint
```
POST /api/auth/admin/refresh
```

**Request:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "new_admin_token",
    "refreshToken": "new_refresh_token"
  }
}
```

### Security Features
- Rate limiting: 10 attempts per 15 minutes
- JWT validation with admin role verification
- Automatic token rotation (new refresh token on each refresh)
- Secure HTTP-only cookie storage

## Frontend Implementation

### 1. Server Actions

#### Import the actions:
```typescript
import { adminRefreshToken, adminLogout } from "@/app/dashboard/login/actions";
```

#### Manual token refresh:
```typescript
const refreshResult = await adminRefreshToken();
if (refreshResult.success) {
  console.log("Token refreshed successfully");
} else {
  console.error("Refresh failed:", refreshResult.error);
}
```

#### Admin logout:
```typescript
const logoutResult = await adminLogout();
if (logoutResult.success) {
  console.log("Logged out successfully");
}
```

### 2. React Hook (Recommended)

#### Basic usage:
```typescript
import { useAdminAuth } from "@/hooks/useAdminAuth";

function AdminComponent() {
  const { 
    isAuthenticated, 
    isLoading, 
    error, 
    refreshToken, 
    logout 
  } = useAdminAuth();

  if (isLoading) return <div>Loading...</div>;
  if (!isAuthenticated) return <div>Not authenticated</div>;

  return (
    <div>
      <h1>Admin Dashboard</h1>
      <button onClick={logout}>Logout</button>
    </div>
  );
}
```

#### Manual refresh:
```typescript
const { refreshToken } = useAdminAuth();

const handleRefresh = async () => {
  const success = await refreshToken();
  if (success) {
    toast.success("Token refreshed");
  } else {
    toast.error("Refresh failed");
  }
};
```

### 3. API Calls with Auto-Refresh

#### Using the enhanced API utility:
```typescript
import { createAuthenticatedAdminApiWithRefresh } from "@/services/api";

async function fetchAdminData() {
  try {
    const api = await createAuthenticatedAdminApiWithRefresh();
    const response = await api.get("/admin/dashboard");
    return response.data;
  } catch (error) {
    if (error.message.includes("Session expired")) {
      // User will be redirected to login automatically
      return null;
    }
    throw error;
  }
}
```

#### Using the hook for API calls:
```typescript
import { useAdminApiWithRefresh } from "@/hooks/useAdminAuth";

function AdminDataComponent() {
  const { makeAuthenticatedRequest } = useAdminApiWithRefresh();
  const [data, setData] = useState(null);

  const fetchData = async () => {
    try {
      const result = await makeAuthenticatedRequest(async () => {
        const api = await createAuthenticatedAdminApi();
        return await api.get("/admin/dashboard");
      });
      setData(result.data);
    } catch (error) {
      console.error("Failed to fetch data:", error);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return <div>{/* Render data */}</div>;
}
```

## Automatic Features

### 1. Background Refresh
- Automatically refreshes tokens every 20 hours
- Runs only when user is authenticated
- Cleans up intervals on component unmount

### 2. Focus-Based Refresh
- Checks authentication when window regains focus
- Refreshes token if needed when user returns
- Handles long periods of inactivity

### 3. API Interceptors
- Automatically retries failed requests after token refresh
- Handles 401 errors gracefully
- Redirects to login if refresh fails

## Error Handling

### Common Scenarios

#### 1. Refresh Token Expired
```typescript
// Automatic handling in useAdminAuth hook
if (!refreshResult.success) {
  // User is redirected to login automatically
  router.push("/dashboard/login");
}
```

#### 2. Network Errors
```typescript
try {
  await adminRefreshToken();
} catch (error) {
  // Fallback to login
  await adminLogout();
}
```

#### 3. Invalid Tokens
```typescript
// Tokens are automatically cleared on validation failure
await clearAuthCookies(true);
```

## Best Practices

### 1. Use the Hook
```typescript
// ✅ Recommended
const { isAuthenticated, logout } = useAdminAuth();

// ❌ Avoid manual token management
const token = await getAuthToken("admin");
```

### 2. Handle Loading States
```typescript
const { isLoading, isAuthenticated } = useAdminAuth();

if (isLoading) {
  return <LoadingSpinner />;
}

if (!isAuthenticated) {
  return <LoginPrompt />;
}
```

### 3. Graceful Error Handling
```typescript
const { error } = useAdminAuth();

if (error) {
  toast.error(error);
}
```

## Integration Examples

### 1. Protected Admin Layout
```typescript
"use client";

import { useAdminAuth } from "@/hooks/useAdminAuth";

export default function AdminLayout({ children }) {
  const { isAuthenticated, isLoading } = useAdminAuth();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    redirect("/dashboard/login");
  }

  return <div className="admin-layout">{children}</div>;
}
```

### 2. Admin API Service
```typescript
class AdminService {
  private api: any;

  constructor() {
    this.initializeApi();
  }

  private async initializeApi() {
    this.api = await createAuthenticatedAdminApiWithRefresh();
  }

  async getDashboardStats() {
    const response = await this.api.get("/admin/stats");
    return response.data;
  }

  async updateUser(userId: string, data: any) {
    const response = await this.api.put(`/admin/users/${userId}`, data);
    return response.data;
  }
}
```

## Troubleshooting

### Common Issues

1. **Token not refreshing**: Check if refresh token exists in cookies
2. **Infinite redirects**: Ensure login page doesn't use admin auth hook
3. **API calls failing**: Verify token format and expiration
4. **Memory leaks**: Ensure proper cleanup of intervals and listeners

### Debug Mode
```typescript
// Enable debug logging
localStorage.setItem("debug", "admin-auth");

// Check token status
console.log(await getAuthToken("admin"));
console.log(await getRefreshToken("admin"));
```

## Security Considerations

1. **Secure Storage**: Tokens stored in HTTP-only cookies
2. **Token Rotation**: New refresh token on each refresh
3. **Rate Limiting**: Prevents brute force attacks
4. **Automatic Cleanup**: Tokens cleared on errors
5. **Role Verification**: Admin role checked on each refresh

This implementation provides a robust, secure, and user-friendly admin authentication system with automatic token management.
